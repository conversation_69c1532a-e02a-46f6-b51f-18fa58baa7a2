import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export class LocalCompilers {
  private tempDir = path.join(process.cwd(), 'temp-compilation');

  constructor() {
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  async compileAndRunJava(code: string, input: any): Promise<{ output: any; error?: string; executionTime: number }> {
    const startTime = Date.now();
    const className = 'Solution';
    const javaFile = path.join(this.tempDir, `${className}.java`);
    const classFile = path.join(this.tempDir, `${className}.class`);

    const javaCode = `
import java.util.*;
import java.io.*;

public class ${className} {
    ${code}
    
    public static void main(String[] args) {
        try {
            ${className} sol = new ${className}();
            String inputJson = "${JSON.stringify(input).replace(/"/g, '\\"')}";
            
            // Simple execution - user must implement main logic
            System.out.println("Execution completed");
        } catch (Exception e) {
            System.err.println("ERROR: " + e.getMessage());
        }
    }
}`;

    try {
      fs.writeFileSync(javaFile, javaCode);

      // Compile
      const compileResult = await this.runCommand('javac', [javaFile]);
      if (compileResult.error) {
        return { output: null, error: compileResult.error, executionTime: Date.now() - startTime };
      }

      // Run
      const runResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
      
      return {
        output: runResult.output || 'Compiled and executed successfully',
        error: runResult.error,
        executionTime: Date.now() - startTime
      };
    } finally {
      [javaFile, classFile].forEach(file => {
        if (fs.existsSync(file)) fs.unlinkSync(file);
      });
    }
  }

  async compileAndRunCpp(code: string, input: any): Promise<{ output: any; error?: string; executionTime: number }> {
    const startTime = Date.now();
    const cppFile = path.join(this.tempDir, `${uuidv4()}.cpp`);
    const exeFile = path.join(this.tempDir, `${uuidv4()}.exe`);

    const cppCode = `
#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
using namespace std;

${code}

int main() {
    try {
        cout << "C++ execution completed" << endl;
        return 0;
    } catch (const exception& e) {
        cerr << "ERROR: " << e.what() << endl;
        return 1;
    }
}`;

    try {
      fs.writeFileSync(cppFile, cppCode);

      // Compile
      const compileResult = await this.runCommand('g++', ['-o', exeFile, cppFile, '-std=c++17']);
      if (compileResult.error) {
        return { output: null, error: compileResult.error, executionTime: Date.now() - startTime };
      }

      // Run
      const runResult = await this.runCommand(exeFile, []);
      
      return {
        output: runResult.output || 'Compiled and executed successfully',
        error: runResult.error,
        executionTime: Date.now() - startTime
      };
    } finally {
      [cppFile, exeFile].forEach(file => {
        if (fs.existsSync(file)) fs.unlinkSync(file);
      });
    }
  }

  async compileAndRunGo(code: string, input: any): Promise<{ output: any; error?: string; executionTime: number }> {
    const startTime = Date.now();
    const goFile = path.join(this.tempDir, `${uuidv4()}.go`);

    const goCode = `
package main

import (
    "fmt"
    "encoding/json"
)

${code}

func main() {
    defer func() {
        if r := recover(); r != nil {
            fmt.Printf("ERROR: %v\\n", r)
        }
    }()
    
    fmt.Println("Go execution completed")
}`;

    try {
      fs.writeFileSync(goFile, goCode);

      const result = await this.runCommand('go', ['run', goFile]);
      
      return {
        output: result.output || 'Go execution completed',
        error: result.error,
        executionTime: Date.now() - startTime
      };
    } finally {
      if (fs.existsSync(goFile)) fs.unlinkSync(goFile);
    }
  }

  private async runCommand(command: string, args: string[]): Promise<{ output?: string; error?: string }> {
    return new Promise((resolve) => {
      const child = spawn(command, args, { timeout: 10000 });
      
      let stdout = '';
      let stderr = '';

      child.stdout?.on('data', (data) => stdout += data.toString());
      child.stderr?.on('data', (data) => stderr += data.toString());

      child.on('close', (code) => {
        if (code !== 0 || stderr) {
          resolve({ error: stderr || `Process exited with code ${code}` });
        } else {
          resolve({ output: stdout.trim() });
        }
      });

      child.on('error', (error) => {
        resolve({ error: error.message });
      });
    });
  }
}