import { useState, useCallback } from "react"

interface TestCase {
  input: any
  expectedOutput: any
  description: string
}

interface ValidationResult {
  passed: boolean
  output: any
  executionTime: number
  memoryUsage: number
  errors: string[]
  testResults: TestCaseResult[]
}

interface TestCaseResult {
  passed: boolean
  input: any
  expectedOutput: any
  actualOutput: any
  description: string
  executionTime: number
}

export const useCodeValidation = () => {
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  const [testCases, setTestCases] = useState<TestCase[]>([])

  const generateTestCases = useCallback(async (problemStatement: string, language: string) => {
    try {
      const response = await window.electronAPI.generateTestCases({
        problemStatement,
        language
      })
      
      if (response.success && response.testCases) {
        setTestCases(response.testCases)
        return response.testCases
      }
    } catch (error) {
      console.error("Failed to generate test cases:", error)
    }
    return []
  }, [])

  const validateCode = useCallback(async (code: string, language: string, customTestCases?: TestCase[]) => {
    if (!code) return

    setIsValidating(true)
    setValidationResult(null)

    try {
      const casesToUse = customTestCases || testCases
      const response = await window.electronAPI.validateCode({
        code,
        language,
        testCases: casesToUse
      })

      if (response.success) {
        setValidationResult(response.result)
        return response.result
      } else {
        console.error("Validation failed:", response.error)
        setValidationResult({
          passed: false,
          output: null,
          executionTime: 0,
          memoryUsage: 0,
          errors: [response.error || "Validation failed"],
          testResults: []
        })
      }
    } catch (error) {
      console.error("Validation error:", error)
      setValidationResult({
        passed: false,
        output: null,
        executionTime: 0,
        memoryUsage: 0,
        errors: [error.message || "Unknown validation error"],
        testResults: []
      })
    } finally {
      setIsValidating(false)
    }
  }, [testCases])

  const clearValidation = useCallback(() => {
    setValidationResult(null)
    setIsValidating(false)
  }, [])

  return {
    validationResult,
    isValidating,
    testCases,
    setTestCases,
    generateTestCases,
    validateCode,
    clearValidation
  }
}