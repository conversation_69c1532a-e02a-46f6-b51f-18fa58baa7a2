import React, { Component, ReactNode } from 'react';
import { ProcessingError } from '../types/errors';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Log processing errors differently
    if (this.isProcessingError(error)) {
      console.error('Processing Error:', {
        type: error.type,
        recoverable: error.recoverable,
        message: error.message
      });
    }
  }

  private isProcessingError(error: Error): error is ProcessingError {
    return 'type' in error && 'recoverable' in error;
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-black flex items-center justify-center p-4">
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6 max-w-md">
            <h2 className="text-red-400 text-lg font-semibold mb-2">Something went wrong</h2>
            <p className="text-white/70 text-sm mb-4">
              {this.state.error?.message || 'An unexpected error occurred'}
            </p>
            <button
              onClick={() => this.setState({ hasError: false, error: null })}
              className="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-4 py-2 rounded text-sm transition"
            >
              Try again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}