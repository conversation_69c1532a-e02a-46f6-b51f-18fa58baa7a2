import { SecureCodeRunner } from './SecureCodeRunner';
import { LocalCompilers } from './LocalCompilers';
import { AITestGenerator } from './AITestGenerator';
import { CodeWrapperGenerator } from './CodeWrapperGenerator';
import type { TestCase } from './AITestGenerator';

export interface ValidationResult {
  passed: boolean;
  output: any;
  executionTime: number;
  memoryUsage: number;
  errors: string[];
  testResults: TestCaseResult[];
}

export interface TestCaseResult {
  passed: boolean;
  input: any;
  expectedOutput: any;
  actualOutput: any;
  description: string;
  executionTime: number;
}

export class CodeValidator {
  private secureRunner = new SecureCodeRunner();
  private localCompilers = new LocalCompilers();
  private aiTestGenerator = new AITestGenerator();

  async validateCode(code: string, language: string, testCases: TestCase[], problemStatement?: string): Promise<ValidationResult> {
    const startTime = Date.now();
    const testResults: TestCaseResult[] = [];
    let allPassed = true;

    if (!testCases.length) {
      return {
        passed: false,
        output: null,
        executionTime: 0,
        memoryUsage: 0,
        errors: ['No test cases provided'],
        testResults: []
      };
    }

    // Auto-wrap code if it doesn't have proper function structure
    const wrappedCode = problemStatement ? 
      CodeWrapperGenerator.wrapCode(code, language, problemStatement) : code;

    for (const testCase of testCases) {
      try {
        const result = await this.executeCode(wrappedCode, language, testCase.input);
        
        if (result.error) {
          testResults.push({
            passed: false,
            input: testCase.input,
            expectedOutput: testCase.expectedOutput,
            actualOutput: null,
            description: testCase.description,
            executionTime: result.executionTime
          });
          allPassed = false;
          continue;
        }

        const passed = this.compareOutputs(result.output, testCase.expectedOutput);
        
        testResults.push({
          passed,
          input: testCase.input,
          expectedOutput: testCase.expectedOutput,
          actualOutput: result.output,
          description: testCase.description,
          executionTime: result.executionTime
        });

        if (!passed) allPassed = false;
      } catch (error) {
        testResults.push({
          passed: false,
          input: testCase.input,
          expectedOutput: testCase.expectedOutput,
          actualOutput: null,
          description: testCase.description,
          executionTime: 0
        });
        allPassed = false;
      }
    }

    return {
      passed: allPassed,
      output: testResults.length > 0 ? testResults[0].actualOutput : null,
      executionTime: testResults.reduce((sum, r) => sum + r.executionTime, 0),
      memoryUsage: testResults.reduce((sum, r) => sum + (r as any).memoryUsage || 0, 0),
      errors: testResults.filter(r => !r.passed).map(r => `${r.description}: Expected ${JSON.stringify(r.expectedOutput)}, got ${JSON.stringify(r.actualOutput)}`),
      testResults
    };
  }

  private async executeCode(code: string, language: string, input: any): Promise<{ output: any; error?: string; executionTime: number; memoryUsage?: number }> {
    const lang = language.toLowerCase();
    
    try {
      switch (lang) {
        case 'python':
          return await this.secureRunner.runPython(code, input);
        
        case 'javascript':
        case 'js':
          return await this.secureRunner.runJavaScript(code, input);
        
        case 'java':
          return await this.localCompilers.compileAndRunJava(code, input);
        
        case 'cpp':
        case 'c++':
          return await this.localCompilers.compileAndRunCpp(code, input);
        
        case 'go':
        case 'golang':
          return await this.localCompilers.compileAndRunGo(code, input);
        
        default:
          return {
            output: null,
            error: `Language ${language} not supported`,
            executionTime: 0
          };
      }
    } catch (error) {
      return {
        output: null,
        error: error.message,
        executionTime: 0
      };
    }
  }

  private compareOutputs(actual: any, expected: any): boolean {
    // Handle null/undefined
    if (actual === null && expected === null) return true;
    if (actual === undefined && expected === undefined) return true;
    if (actual == null || expected == null) return false;

    // Handle arrays
    if (Array.isArray(actual) && Array.isArray(expected)) {
      if (actual.length !== expected.length) return false;
      return actual.every((item, index) => this.compareOutputs(item, expected[index]));
    }

    // Handle objects
    if (typeof actual === 'object' && typeof expected === 'object') {
      const actualKeys = Object.keys(actual).sort();
      const expectedKeys = Object.keys(expected).sort();
      
      if (actualKeys.length !== expectedKeys.length) return false;
      if (!actualKeys.every(key => expectedKeys.includes(key))) return false;
      
      return actualKeys.every(key => this.compareOutputs(actual[key], expected[key]));
    }

    // Handle primitives with type coercion
    return String(actual) === String(expected);
  }

  async generateTestCases(problemStatement: string, language: string): Promise<TestCase[]> {
    return await this.aiTestGenerator.generateTestCases(problemStatement, language);
  }

  async autoValidateCode(code: string, language: string, options?: { problemStatement?: string; screenshots?: string[] }): Promise<ValidationResult> {
    const testCases = options?.problemStatement 
      ? await this.generateTestCases(options.problemStatement, language)
      : [{ input: 'test', expectedOutput: 'test', description: 'Basic test' }];
    
    return this.validateCode(code, language, testCases, options?.problemStatement);
  }
}

