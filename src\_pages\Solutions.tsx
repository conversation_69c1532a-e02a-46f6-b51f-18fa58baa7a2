// Solutions.tsx
import React, { useState, useEffect, useRef } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter"
import { dracula } from "react-syntax-highlighter/dist/esm/styles/prism"
import { MousePointer2 } from "lucide-react"

import ScreenshotQueue from "../components/Queue/ScreenshotQueue"

import { ProblemStatementData } from "../types/solutions"
import { Solution, SingleSolution } from "../types/index"
import SolutionCommands from "../components/Solutions/SolutionCommands"
import Debug from "./Debug"
import { useToast } from "../contexts/toast"
import { COMMAND_KEY } from "../utils/platform"
import { MarkdownRenderer } from "../components/ui/markdown"
import { ValidationStatus } from "../components/Solutions/ValidationStatus"
import { TestCaseEditor } from "../components/Solutions/TestCaseEditor"
import { PerformanceMetrics } from "../components/Solutions/PerformanceMetrics"
import { SolutionSelector } from "../components/Solutions/SolutionSelector"
import { CapacityPlanning } from "../components/Solutions/CapacityPlanning"

export const ContentSection = ({
  title,
  content,
  isLoading
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
}) => (
  <div className="space-y-2">
    <h2 className="text-[13px] font-medium text-white tracking-wide">
      {title}
    </h2>
    {isLoading ? (
      <div className="mt-4 flex">
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          Extracting problem statement...
        </p>
      </div>
    ) : (
      <div className="text-[13px] leading-[1.4] text-gray-100 max-w-[600px]">
        {content}
      </div>
    )}
  </div>
)
const SolutionSection = ({
  title,
  content,
  isLoading,
  currentLanguage,
  problemStatement
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
  currentLanguage: string
  problemStatement?: string
}) => {
  const [copied, setCopied] = useState(false)
  const [validationResult, setValidationResult] = useState(null)
  const [isValidating, setIsValidating] = useState(false)
  const [testCases, setTestCases] = useState([])

  // Generate test cases when problem statement is available
  useEffect(() => {
    if (problemStatement && !testCases.length) {
      generateTestCases()
    }
  }, [problemStatement])

  const generateTestCases = async () => {
    try {
      const response = await window.electronAPI.generateTestCases({
        problemStatement,
        language: currentLanguage
      })
      if (response.success) {
        setTestCases(response.testCases || [])
      }
    } catch (error) {
      console.error("Failed to generate test cases:", error)
    }
  }

  const validateCode = async () => {
    if (!content || typeof content !== "string") return
    
    setIsValidating(true)
    try {
      const response = await window.electronAPI.validateCode({
        code: content,
        language: currentLanguage,
        testCases,
        problemStatement
      })
      
      if (response.success) {
        setValidationResult(response.result)
      } else {
        console.error("Validation failed:", response.error)
      }
    } catch (error) {
      console.error("Validation error:", error)
    } finally {
      setIsValidating(false)
    }
  }

  const autoValidateCode = async () => {
    if (!content || typeof content !== "string") return
    
    setIsValidating(true)
    try {
      // Get current screenshots for test case extraction
      const screenshotsData = await window.electronAPI.getScreenshots()
      const screenshots = screenshotsData?.map(s => s.preview) || []
      
      const response = await window.electronAPI.autoValidateCode({
        code: content,
        language: currentLanguage,
        problemStatement,
        screenshots
      })
      
      if (response.success) {
        setValidationResult(response.result)
        // Auto-generate test cases from the result
        if (response.result.testResults) {
          const generatedTestCases = response.result.testResults.map(tr => ({
            input: tr.input,
            expectedOutput: tr.expectedOutput,
            description: tr.description
          }))
          setTestCases(generatedTestCases)
        }
      } else {
        console.error("Auto-validation failed:", response.error)
      }
    } catch (error) {
      console.error("Auto-validation error:", error)
    } finally {
      setIsValidating(false)
    }
  }

  const copyToClipboard = () => {
    if (typeof content === "string") {
      navigator.clipboard.writeText(content).then(() => {
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      })
    }
  }

  return (
    <div className="space-y-2 relative">
      <h2 className="text-[13px] font-medium text-white tracking-wide">
        {title}
      </h2>
      {isLoading ? (
        <div className="space-y-1.5">
          <div className="mt-4 flex">
            <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
              Loading solutions...
            </p>
          </div>
        </div>
      ) : (
        <div className="w-full relative">
          <div className="mb-3 space-y-3">
            <TestCaseEditor
              testCases={testCases}
              onTestCasesChange={setTestCases}
              onRunTests={validateCode}
              onAutoTest={autoValidateCode}
              isRunning={isValidating}
            />
            <ValidationStatus
              validationResult={validationResult}
              isValidating={isValidating}
              onValidate={validateCode}
            />
            {validationResult && (
              <PerformanceMetrics
                executionTime={validationResult.executionTime}
                memoryUsage={validationResult.memoryUsage}
                passed={validationResult.passed}
                testCount={validationResult.testResults.length}
                passedCount={validationResult.testResults.filter(t => t.passed).length}
              />
            )}
          </div>
          <div className="absolute top-16 right-2 flex gap-2">
            <button
              onClick={copyToClipboard}
              className="text-xs text-white bg-white/10 hover:bg-white/20 rounded px-2 py-1 transition"
            >
              {copied ? "Copied!" : "Copy Code"}
            </button>
            <button
              onClick={copyToClipboard}
              className="text-xs text-white bg-blue-500/20 hover:bg-blue-500/30 rounded px-2 py-1 transition"
            >
              Copy All
            </button>
            <button
              onClick={() => {
                const solution = {
                  timestamp: new Date().toISOString(),
                  language: currentLanguage,
                  code: content
                };
                const blob = new Blob([JSON.stringify(solution, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `solution-${Date.now()}.json`;
                a.click();
                URL.revokeObjectURL(url);
              }}
              className="text-xs text-white bg-green-500/20 hover:bg-green-500/30 rounded px-2 py-1 transition"
            >
              Export
            </button>
          </div>
          <SyntaxHighlighter
            showLineNumbers
            language={currentLanguage == "golang" ? "go" : currentLanguage}
            style={dracula}
            customStyle={{
              maxWidth: "100%",
              margin: 0,
              padding: "1rem",
              whiteSpace: "pre-wrap",
              wordBreak: "break-all",
              backgroundColor: "rgba(22, 27, 34, 0.5)",
              fontSize: "inherit"
            }}
            wrapLongLines={true}
          >
            {content as string}
          </SyntaxHighlighter>
        </div>
      )}
    </div>
  )
}

export const ComplexitySection = ({
  timeComplexity,
  spaceComplexity,
  isLoading
}: {
  timeComplexity: string | null
  spaceComplexity: string | null
  isLoading: boolean
}) => {
  // Helper to ensure we have proper complexity values
  const formatComplexity = (complexity: string | null): string => {
    // Default if no complexity returned by LLM
    if (!complexity || complexity.trim() === "") {
      return "Complexity not available";
    }

    const bigORegex = /O\([^)]+\)/i;
    // Return the complexity as is if it already has Big O notation
    if (bigORegex.test(complexity)) {
      return complexity;
    }
    
    // Concat Big O notation to the complexity
    return `O(${complexity})`;
  };
  
  const formattedTimeComplexity = formatComplexity(timeComplexity);
  const formattedSpaceComplexity = formatComplexity(spaceComplexity);
  
  return (
    <div className="space-y-2">
      <h2 className="text-[13px] font-medium text-white tracking-wide">
        Complexity
      </h2>
      {isLoading ? (
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          Calculating complexity...
        </p>
      ) : (
        <div className="space-y-3">
          <div className="text-[13px] leading-[1.4] text-gray-100 bg-white/5 rounded-md p-3">
            <div className="flex items-start gap-2">
              <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
              <div>
                <strong>Time:</strong> {formattedTimeComplexity}
              </div>
            </div>
          </div>
          <div className="text-[13px] leading-[1.4] text-gray-100 bg-white/5 rounded-md p-3">
            <div className="flex items-start gap-2">
              <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
              <div>
                <strong>Space:</strong> {formattedSpaceComplexity}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export interface SolutionsProps {
  setView: (view: "queue" | "solutions" | "debug") => void
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}
const Solutions: React.FC<SolutionsProps> = ({
  setView,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const queryClient = useQueryClient()
  const contentRef = useRef<HTMLDivElement>(null)

  const [debugProcessing, setDebugProcessing] = useState(false)
  const [problemStatementData, setProblemStatementData] =
    useState<ProblemStatementData | null>(null)
  const [solutionData, setSolutionData] = useState<Solution | null>(null)
  const [selectedSolutionType, setSelectedSolutionType] = useState<'brute_force' | 'less_optimal' | 'most_optimal'>('brute_force')
  const [fontSize, setFontSize] = useState(14)

  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const [tooltipHeight, setTooltipHeight] = useState(0)

  const [isResetting, setIsResetting] = useState(false)
  const [questionMode, setQuestionMode] = useState<"dsa" | "system-design" | "auto-detect">("auto-detect")

  interface Screenshot {
    id: string
    path: string
    preview: string
    timestamp: number
  }

  const [extraScreenshots, setExtraScreenshots] = useState<Screenshot[]>([])

  useEffect(() => {
    const fetchScreenshots = async () => {
      try {
        const existing = await window.electronAPI.getScreenshots()
        console.log("Raw screenshot data:", existing)
        const screenshots = (Array.isArray(existing) ? existing : []).map(
          (p) => ({
            id: p.path,
            path: p.path,
            preview: p.preview,
            timestamp: Date.now()
          })
        )
        console.log("Processed screenshots:", screenshots)
        setExtraScreenshots(screenshots)
      } catch (error) {
        console.error("Error loading extra screenshots:", error)
        setExtraScreenshots([])
      }
    }

    fetchScreenshots()
  }, [solutionData])

  // Load question mode from config
  useEffect(() => {
    const loadQuestionMode = async () => {
      try {
        const config = await window.electronAPI.getConfig();
        setQuestionMode(config.questionDetectionMode || "auto-detect");
      } catch (error) {
        console.error("Failed to load question mode:", error);
      }
    };
    loadQuestionMode();
  }, []);

  // Save question mode when changed
  const handleQuestionModeChange = async (mode: "dsa" | "system-design" | "auto-detect") => {
    setQuestionMode(mode);
    try {
      await window.electronAPI.updateConfig({ questionDetectionMode: mode });
      showToast("Mode Updated", `Switched to ${mode === 'dsa' ? 'DSA' : mode === 'system-design' ? 'System Design' : 'Auto-Detect'} mode`, "success");
    } catch (error) {
      console.error("Failed to save question mode:", error);
    }
  };

  const { showToast } = useToast()

  // Handle wheel scroll for font size control
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault()
        const delta = e.deltaY > 0 ? -1 : 1
        setFontSize(prev => Math.max(10, Math.min(24, prev + delta)))
      }
    }

    const element = contentRef.current
    if (element) {
      element.addEventListener('wheel', handleWheel, { passive: false })
      return () => element.removeEventListener('wheel', handleWheel)
    }
  }, [])

  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        if (isTooltipVisible) {
          contentHeight += tooltipHeight
        }
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(async () => {
        try {
          const existing = await window.electronAPI.getScreenshots()
          const screenshots = (Array.isArray(existing) ? existing : []).map(
            (p) => ({
              id: p.path,
              path: p.path,
              preview: p.preview,
              timestamp: Date.now()
            })
          )
          setExtraScreenshots(screenshots)
        } catch (error) {
          console.error("Error loading extra screenshots:", error)
        }
      }),
      window.electronAPI.onResetView(() => {
        // Set resetting state first
        setIsResetting(true)

        // Remove queries
        queryClient.removeQueries({
          queryKey: ["solution"]
        })
        queryClient.removeQueries({
          queryKey: ["new_solution"]
        })

        // Reset screenshots
        setExtraScreenshots([])

        // After a small delay, clear the resetting state
        setTimeout(() => {
          setIsResetting(false)
        }, 0)
      }),
      window.electronAPI.onSolutionStart(() => {
        // Every time processing starts, reset relevant states
        setSolutionData(null)
        setSelectedSolutionType('brute_force')
      }),
      window.electronAPI.onProblemExtracted((data) => {
        queryClient.setQueryData(["problem_statement"], data)
      }),
      //if there was an error processing the initial solution
      window.electronAPI.onSolutionError((error: string) => {
        showToast("Processing Failed", error, "error")
        const solution = queryClient.getQueryData(["solution"]) as Solution | null
        if (!solution) {
          setView("queue")
        }
        setSolutionData(solution)
        console.error("Processing error:", error)
      }),
      //when the initial solution is generated, we'll set the solution data to that
      window.electronAPI.onSolutionSuccess((data) => {
        if (!data) {
          console.warn("Received empty or invalid solution data")
          return
        }
        console.log({ data })
        
        queryClient.setQueryData(["solution"], data)
        setSolutionData(data)
        setSelectedSolutionType(data.selectedSolution || 'brute_force')

        // Fetch latest screenshots when solution is successful
        const fetchScreenshots = async () => {
          try {
            const existing = await window.electronAPI.getScreenshots()
            const screenshots =
              existing.previews?.map((p) => ({
                id: p.path,
                path: p.path,
                preview: p.preview,
                timestamp: Date.now()
              })) || []
            setExtraScreenshots(screenshots)
          } catch (error) {
            console.error("Error loading extra screenshots:", error)
            setExtraScreenshots([])
          }
        }
        fetchScreenshots()
      }),

      //########################################################
      //DEBUG EVENTS
      //########################################################
      window.electronAPI.onDebugStart(() => {
        //we'll set the debug processing state to true and use that to render a little loader
        setDebugProcessing(true)
      }),
      //the first time debugging works, we'll set the view to debug and populate the cache with the data
      window.electronAPI.onDebugSuccess((data) => {
        queryClient.setQueryData(["new_solution"], data)
        setDebugProcessing(false)
      }),
      //when there was an error in the initial debugging, we'll show a toast and stop the little generating pulsing thing.
      window.electronAPI.onDebugError(() => {
        showToast(
          "Processing Failed",
          "There was an error debugging your code.",
          "error"
        )
        setDebugProcessing(false)
      }),
      window.electronAPI.onProcessingNoScreenshots(() => {
        showToast(
          "No Screenshots",
          "There are no extra screenshots to process.",
          "neutral"
        )
      }),
      // Question mode toggle shortcut handler
      window.electronAPI.onToggleQuestionMode(() => {
        const modes: ("dsa" | "system-design" | "auto-detect")[] = ["dsa", "system-design", "auto-detect"];
        const currentIndex = modes.indexOf(questionMode);
        const nextMode = modes[(currentIndex + 1) % modes.length];
        handleQuestionModeChange(nextMode);
      }),
      // Solution switching shortcut handler
      window.electronAPI.onSwitchSolution((solutionType: string) => {
        if (solutionData && ['brute_force', 'less_optimal', 'most_optimal'].includes(solutionType)) {
          setSelectedSolutionType(solutionType as 'brute_force' | 'less_optimal' | 'most_optimal');
        }
      }),
      // Removed out of credits handler - unlimited credits in this version
    ]

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [isTooltipVisible, tooltipHeight, solutionData, questionMode])

  useEffect(() => {
    setProblemStatementData(
      queryClient.getQueryData(["problem_statement"]) || null
    )
    setSolutionData(queryClient.getQueryData(["solution"]) || null)

    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event?.query.queryKey[0] === "problem_statement") {
        setProblemStatementData(
          queryClient.getQueryData(["problem_statement"]) || null
        )
      }
      if (event?.query.queryKey[0] === "solution") {
        const solution = queryClient.getQueryData(["solution"]) as Solution | null
        setSolutionData(solution)
      }
    })
    return () => unsubscribe()
  }, [queryClient])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    setIsTooltipVisible(visible)
    setTooltipHeight(height)
  }

  const handleDeleteExtraScreenshot = async (index: number) => {
    const screenshotToDelete = extraScreenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        // Fetch and update screenshots after successful deletion
        const existing = await window.electronAPI.getScreenshots()
        const screenshots = (Array.isArray(existing) ? existing : []).map(
          (p) => ({
            id: p.path,
            path: p.path,
            preview: p.preview,
            timestamp: Date.now()
          })
        )
        setExtraScreenshots(screenshots)
      } else {
        console.error("Failed to delete extra screenshot:", response.error)
        showToast("Error", "Failed to delete the screenshot", "error")
      }
    } catch (error) {
      console.error("Error deleting extra screenshot:", error)
      showToast("Error", "Failed to delete the screenshot", "error")
    }
  }

  return (
    <>
      {!isResetting && queryClient.getQueryData(["new_solution"]) ? (
        <Debug
          isProcessing={debugProcessing}
          setIsProcessing={setDebugProcessing}
          currentLanguage={currentLanguage}
          setLanguage={setLanguage}
        />
      ) : (
        <div ref={contentRef} className="relative" style={{ fontSize: `${fontSize}px` }}>
          <div className="space-y-3 px-4 py-3">
          {/* Question Mode Toggle */}
          <div className="flex gap-1 mb-3">
            <button
              onClick={() => handleQuestionModeChange("dsa")}
              className={`px-3 py-1 text-xs rounded transition ${
                questionMode === "dsa"
                  ? "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                  : "bg-white/5 text-white/70 hover:bg-white/10"
              }`}
            >
              🧮 DSA
            </button>
            <button
              onClick={() => handleQuestionModeChange("system-design")}
              className={`px-3 py-1 text-xs rounded transition ${
                questionMode === "system-design"
                  ? "bg-green-500/20 text-green-400 border border-green-500/30"
                  : "bg-white/5 text-white/70 hover:bg-white/10"
              }`}
            >
              🏗️ System Design
            </button>
            <button
              onClick={() => handleQuestionModeChange("auto-detect")}
              className={`px-3 py-1 text-xs rounded transition ${
                questionMode === "auto-detect"
                  ? "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                  : "bg-white/5 text-white/70 hover:bg-white/10"
              }`}
            >
              🤖 Auto
            </button>
          </div>

          {/* Conditionally render the screenshot queue if solutionData is available */}
          {solutionData && (
            <div className="bg-transparent w-fit">
              <div className="pb-3">
                <div className="space-y-3 w-fit">
                  <ScreenshotQueue
                    isLoading={debugProcessing}
                    screenshots={extraScreenshots}
                    onDeleteScreenshot={handleDeleteExtraScreenshot}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Navbar of commands with the SolutionsHelper */}
          <SolutionCommands
            onTooltipVisibilityChange={handleTooltipVisibilityChange}
            isProcessing={!problemStatementData || !solutionData}
            extraScreenshots={extraScreenshots}
            credits={credits}
            currentLanguage={currentLanguage}
            setLanguage={setLanguage}
          />

          {/* Font Size Control */}
          <div className="flex items-center gap-2 text-xs text-white/60 mb-2">
            <MousePointer2 size={12} />
            <span>Ctrl+Scroll to adjust font size ({fontSize}px)</span>
          </div>

          {/* Main Content - Modified width constraints */}
          <div className="w-full text-black bg-black/60 rounded-md">
            <div className="rounded-lg overflow-hidden">
              <div className="px-4 py-3 space-y-4 max-w-full">
                {!solutionData && (
                  <>
                    <ContentSection
                      title="Problem Statement"
                      content={problemStatementData?.problem_statement}
                      isLoading={!problemStatementData}
                    />
                    {problemStatementData && (
                      <div className="mt-4 flex">
                        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
                          Generating solutions...
                        </p>
                      </div>
                    )}
                  </>
                )}

                {solutionData && (
                  <>
                    <SolutionSelector
                      selectedSolution={selectedSolutionType}
                      onSolutionChange={setSelectedSolutionType}
                      isSystemDesign={solutionData.isSystemDesign}
                    />
                    
                    {(() => {
                      const currentSolution = solutionData.solutions[selectedSolutionType];
                      
                      if (solutionData.isSystemDesign) {
                        return (
                          <>
                            <CapacityPlanning
                              capacityPlanning={solutionData.capacityPlanning}
                              techStack={solutionData.techStack}
                              scale={currentSolution.scale}
                              architecture={currentSolution.architecture}
                            />
                            
                            <ContentSection
                              title="Architecture Analysis"
                              content={
                                <div className="space-y-4">
                                  {currentSolution.thoughts.map((thought, index) => (
                                    <div key={index} className="bg-white/5 rounded-lg p-4">
                                      <MarkdownRenderer 
                                        content={thought} 
                                        className="text-sm leading-relaxed"
                                      />
                                    </div>
                                  ))}
                                </div>
                              }
                              isLoading={!currentSolution.thoughts}
                            />

                            <SolutionSection
                              title="Implementation Examples"
                              content={currentSolution.code}
                              isLoading={!currentSolution.code}
                              currentLanguage={currentLanguage}
                              problemStatement={problemStatementData?.problem_statement}
                            />
                            
                            {solutionData.fullResponse && (
                              <ContentSection
                                title="Complete System Design"
                                content={
                                  <div className="bg-white/5 rounded-lg p-4">
                                    <MarkdownRenderer 
                                      content={solutionData.fullResponse} 
                                      className="text-sm leading-relaxed"
                                    />
                                  </div>
                                }
                                isLoading={false}
                              />
                            )}
                          </>
                        );
                      }
                      
                      return (
                        <>
                          <ContentSection
                            title={`My Thoughts (${COMMAND_KEY} + Arrow keys to scroll)`}
                            content={
                              currentSolution.thoughts && (
                                <div className="space-y-3">
                                  <div className="space-y-3">
                                    {currentSolution.thoughts.map((thought, index) => (
                                      <div key={index} className="bg-white/5 rounded-lg p-3">
                                        <MarkdownRenderer 
                                          content={thought} 
                                          className="text-sm leading-relaxed"
                                        />
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )
                            }
                            isLoading={!currentSolution.thoughts}
                          />

                          <SolutionSection
                            title="Solution"
                            content={currentSolution.code}
                            isLoading={!currentSolution.code}
                            currentLanguage={currentLanguage}
                            problemStatement={problemStatementData?.problem_statement}
                          />

                          <ComplexitySection
                            timeComplexity={currentSolution.time_complexity}
                            spaceComplexity={currentSolution.space_complexity}
                            isLoading={!currentSolution.time_complexity || !currentSolution.space_complexity}
                          />
                        </>
                      );
                    })()
                    }
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      )}
    </>
  )
}

export default Solutions