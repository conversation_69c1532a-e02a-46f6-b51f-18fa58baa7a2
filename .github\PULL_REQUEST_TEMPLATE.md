# Pull Request

## 📋 Description
<!-- Provide a clear and concise description of your changes -->

## 🔧 Type of Change
<!-- Mark the relevant option with an "x" -->
- [ ] 🐛 Bug fix (non-breaking change that fixes an issue)
- [ ] ✨ New feature (non-breaking change that adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🎨 Style/UI changes
- [ ] ♻️ Code refactoring
- [ ] 🧪 Test additions or updates
- [ ] 🔧 Build/CI changes

## 🎯 Related Issues
<!-- Link to related issues using keywords like "Fixes #123" or "Closes #456" -->
- Fixes #
- Related to #

## 🧪 Testing
<!-- Describe how you tested your changes -->
- [ ] Manual testing completed
- [ ] Automated tests pass (`npm test`)
- [ ] Linting passes (`npm run lint`)
- [ ] Build succeeds (`npm run build`)
- [ ] Cross-platform testing (Windows/macOS/Linux)

### Test Cases
<!-- List specific test cases you verified -->
1. 
2. 
3. 

## 📸 Screenshots/Videos
<!-- Add screenshots for UI changes or videos for complex features -->
<!-- Use drag & drop or paste images directly -->

**Before:**
<!-- Screenshot of current state -->

**After:**
<!-- Screenshot of your changes -->

## 🔄 Breaking Changes
<!-- If this is a breaking change, describe what breaks and how to migrate -->
- [ ] This PR introduces breaking changes
- [ ] Migration guide provided
- [ ] Changelog updated

### Migration Steps
<!-- If breaking changes, provide migration steps -->
1. 
2. 
3. 

## ✅ Checklist
<!-- Mark completed items with "x" -->

### Code Quality
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is properly commented
- [ ] No console.log statements in production code
- [ ] TypeScript types are properly defined
- [ ] Error handling is implemented

### Documentation
- [ ] README updated (if needed)
- [ ] CONTRIBUTING.md updated (if needed)
- [ ] Code comments added for complex logic
- [ ] API documentation updated (if applicable)

### Security
- [ ] No API keys or secrets in code
- [ ] Input validation implemented
- [ ] External dependencies reviewed
- [ ] Security best practices followed

### Performance
- [ ] Performance impact considered
- [ ] Memory leaks checked
- [ ] Bundle size impact minimal
- [ ] Async operations properly handled

## 🚀 Deployment Notes
<!-- Any special deployment considerations -->
- [ ] Database migrations required
- [ ] Environment variables updated
- [ ] Third-party service configuration needed
- [ ] Build process changes

## 📝 Additional Notes
<!-- Any additional information, concerns, or questions -->

## 🔍 Review Focus Areas
<!-- Highlight specific areas where you want reviewer attention -->
- 
- 
- 

---

### For Reviewers
<!-- This section is for reviewers -->
- [ ] Code review completed
- [ ] Testing verified
- [ ] Documentation reviewed
- [ ] Security considerations checked
- [ ] Performance impact assessed