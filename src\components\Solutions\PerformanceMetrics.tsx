import React from "react"
import { <PERSON>, <PERSON><PERSON>, CheckCircle, XCircle } from "lucide-react"

interface PerformanceMetricsProps {
  executionTime: number
  memoryUsage: number
  passed: boolean
  testCount: number
  passedCount: number
}

export const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({
  executionTime,
  memoryUsage,
  passed,
  testCount,
  passedCount
}) => {
  const successRate = testCount > 0 ? (passedCount / testCount) * 100 : 0

  return (
    <div className="bg-white/5 rounded-lg p-3 space-y-2">
      <div className="flex items-center gap-2 text-xs font-medium text-white">
        {passed ? (
          <CheckCircle size={14} className="text-green-400" />
        ) : (
          <XCircle size={14} className="text-red-400" />
        )}
        Performance Metrics
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <div className="flex items-center gap-2">
          <Clock size={12} className="text-blue-400" />
          <div>
            <div className="text-xs text-white/60">Execution Time</div>
            <div className="text-xs text-white font-mono">{executionTime}ms</div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Cpu size={12} className="text-purple-400" />
          <div>
            <div className="text-xs text-white/60">Memory Usage</div>
            <div className="text-xs text-white font-mono">
              {memoryUsage > 0 ? `${memoryUsage}MB` : 'N/A'}
            </div>
          </div>
        </div>
      </div>
      
      <div className="pt-2 border-t border-white/10">
        <div className="flex items-center justify-between">
          <span className="text-xs text-white/60">Test Results</span>
          <span className="text-xs text-white font-mono">
            {passedCount}/{testCount} passed ({successRate.toFixed(0)}%)
          </span>
        </div>
        
        <div className="mt-1 w-full bg-black/30 rounded-full h-1.5">
          <div 
            className={`h-1.5 rounded-full transition-all duration-300 ${
              passed ? 'bg-green-400' : 'bg-red-400'
            }`}
            style={{ width: `${successRate}%` }}
          />
        </div>
      </div>
    </div>
  )
}