export interface Screenshot {
  id: string
  path: string
  timestamp: number
  thumbnail: string // Base64 thumbnail
}

export interface SingleSolution {
  code: string
  thoughts: string[]
  time_complexity: string
  space_complexity: string
}

export interface Solution {
  solutions: {
    brute_force: SingleSolution
    less_optimal: SingleSolution
    most_optimal: SingleSolution
  }
  selectedSolution: 'brute_force' | 'less_optimal' | 'most_optimal'
  isSystemDesign?: boolean
  fullResponse?: string
}

// Legacy interface for backward compatibility
export interface LegacySolution {
  initial_thoughts: string[]
  thought_steps: string[]
  description: string
  code: string
}
