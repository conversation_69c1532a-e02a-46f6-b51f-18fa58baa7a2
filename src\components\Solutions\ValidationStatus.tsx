import React, { useEffect } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"

interface ValidationResult {
  passed: boolean
  output: any
  executionTime: number
  memoryUsage: number
  errors: string[]
  testResults: TestCaseResult[]
}

interface TestCaseResult {
  passed: boolean
  input: any
  expectedOutput: any
  actualOutput: any
  description: string
  executionTime: number
}

interface ValidationStatusProps {
  validationResult: ValidationResult | null
  isValidating: boolean
  onValidate: () => void
}

interface ValidationStatusProps {
  validationResult: ValidationResult | null
  isValidating: boolean
  onValidate: () => void
}

export const ValidationStatus: React.FC<ValidationStatusProps> = ({
  validationResult,
  isValidating,
  onValidate
}) => {
  if (isValidating) {
    return (
      <div className="flex items-center gap-2 text-blue-400 animate-pulse">
        <Clock size={16} />
        <span className="text-xs">Validating code...</span>
      </div>
    )
  }

  if (!validationResult) {
    return (
      <button
        onClick={onValidate}
        className="flex items-center gap-2 text-xs text-white bg-blue-500/20 hover:bg-blue-500/30 rounded px-2 py-1 transition"
      >
        <CheckCircle size={14} />
        Validate Code
      </button>
    )
  }

  const { passed, executionTime, testResults, errors } = validationResult

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        {passed ? (
          <div className="flex items-center gap-2 text-green-400">
            <CheckCircle size={16} />
            <span className="text-xs">Code validated ✓</span>
            <span className="text-xs opacity-70">({executionTime}ms)</span>
          </div>
        ) : (
          <div className="flex items-center gap-2 text-red-400">
            <XCircle size={16} />
            <span className="text-xs">Validation failed</span>
          </div>
        )}
        
        <button
          onClick={onValidate}
          className="text-xs text-white bg-white/10 hover:bg-white/20 rounded px-2 py-1 transition ml-2"
        >
          Re-validate
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="space-y-1">
          <div className="text-xs text-gray-300 font-medium">Test Results:</div>
          {testResults.map((test, i) => (
            <div
              key={i}
              className={`text-xs p-2 rounded border-l-2 ${
                test.passed
                  ? "bg-green-500/10 border-green-400 text-green-100"
                  : "bg-red-500/10 border-red-400 text-red-100"
              }`}
            >
              <div className="flex items-center gap-2 mb-1">
                {test.passed ? (
                  <CheckCircle size={12} className="text-green-400" />
                ) : (
                  <XCircle size={12} className="text-red-400" />
                )}
                <span className="font-medium">{test.description}</span>
                <span className="opacity-70">({test.executionTime}ms)</span>
              </div>
              
              <div className="space-y-1 text-xs opacity-80">
                <div>Input: {JSON.stringify(test.input)}</div>
                <div>Expected: {JSON.stringify(test.expectedOutput)}</div>
                {!test.passed && (
                  <div className="text-red-300">
                    Got: {JSON.stringify(test.actualOutput)}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {errors.length > 0 && (
        <div className="space-y-1">
          <div className="text-xs text-red-300 font-medium">Errors:</div>
          {errors.map((error, i) => (
            <div key={i} className="text-xs text-red-200 bg-red-500/10 p-2 rounded">
              {error}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}