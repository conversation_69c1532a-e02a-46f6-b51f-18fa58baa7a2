name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  build:
    runs-on: ${{ matrix.os }}
    
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
        
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: |
        npm run clean
        npm run build
        
    - name: Build Windows
      if: matrix.os == 'windows-latest'
      run: npm run package-win
      
    - name: Build macOS
      if: matrix.os == 'macos-latest'
      run: npm run package-mac
      
    - name: Build Linux
      if: matrix.os == 'ubuntu-latest'
      run: npm run package
      
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: CodeCops-${{ matrix.os }}
        path: release/
        
  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        path: artifacts/
        
    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: artifacts/**/*
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}