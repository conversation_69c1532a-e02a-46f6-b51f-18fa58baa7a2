export class CodeWrapperGenerator {
  static wrapCode(code: string, language: string, problemStatement: string): string {
    const lang = language.toLowerCase();
    
    if (lang === 'python') {
      return this.wrapPythonCode(code, problemStatement);
    } else if (lang === 'javascript' || lang === 'js') {
      return this.wrapJavaScriptCode(code, problemStatement);
    }
    
    return code;
  }

  private static wrapPythonCode(code: string, problemStatement: string): string {
    // Check if code already has a function
    if (code.includes('def ') && (code.includes('solution') || code.includes('solve') || code.includes('main'))) {
      return code;
    }

    // Detect problem type and generate appropriate wrapper
    const lower = problemStatement.toLowerCase();
    let functionName = 'solution';
    let parameters = 's, numRows';
    
    if (lower.includes('two sum')) {
      parameters = 'nums, target';
    } else if (lower.includes('palindrome')) {
      parameters = 's';
    } else if (lower.includes('reverse')) {
      parameters = 's';
    } else if (lower.includes('zigzag')) {
      parameters = 's, numRows';
    }

    return `def ${functionName}(${parameters}):
    ${code.split('\n').map(line => '    ' + line).join('\n')}

# Auto-generated wrapper function`;
  }

  private static wrapJavaScriptCode(code: string, problemStatement: string): string {
    // Check if code already has a function
    if ((code.includes('function ') || code.includes('=>')) && 
        (code.includes('solution') || code.includes('solve') || code.includes('main'))) {
      return code;
    }

    // Detect problem type and generate appropriate wrapper
    const lower = problemStatement.toLowerCase();
    let functionName = 'solution';
    let parameters = 's, numRows';
    
    if (lower.includes('two sum')) {
      parameters = 'nums, target';
    } else if (lower.includes('palindrome')) {
      parameters = 's';
    } else if (lower.includes('reverse')) {
      parameters = 's';
    } else if (lower.includes('zigzag')) {
      parameters = 's, numRows';
    }

    return `function ${functionName}(${parameters}) {
    ${code.split('\n').map(line => '    ' + line).join('\n')}
}

// Auto-generated wrapper function`;
  }
}