interface QueuedRequest {
  id: string;
  request: () => Promise<any>;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  retries: number;
  maxRetries: number;
  priority: 'high' | 'normal' | 'low';
  userTier: 'premium' | 'free';
}

class RequestQueue {
  private queue: QueuedRequest[] = [];
  private processing = false;
  private activeRequests = 0;
  private readonly maxConcurrent = 5;
  private readonly delayBetweenRequests = 200;

  async add<T>(
    request: () => Promise<T>,
    maxRetries = 2,
    priority: 'high' | 'normal' | 'low' = 'normal',
    userTier: 'premium' | 'free' = 'free'
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const queuedRequest: QueuedRequest = {
        id: Math.random().toString(36).substr(2, 9),
        request,
        resolve,
        reject,
        retries: 0,
        maxRetries,
        priority,
        userTier
      };

      this.insertByPriority(queuedRequest);
      this.processQueue();
    });
  }

  private insertByPriority(request: QueuedRequest) {
    const priorityOrder = { high: 0, normal: 1, low: 2 };
    const tierOrder = { premium: 0, free: 1 };
    
    let insertIndex = this.queue.length;
    
    for (let i = 0; i < this.queue.length; i++) {
      const existing = this.queue[i];
      const requestScore = priorityOrder[request.priority] + tierOrder[request.userTier];
      const existingScore = priorityOrder[existing.priority] + tierOrder[existing.userTier];
      
      if (requestScore < existingScore) {
        insertIndex = i;
        break;
      }
    }
    
    this.queue.splice(insertIndex, 0, request);
  }

  private async processQueue() {
    if (this.processing) return;
    this.processing = true;

    while (this.queue.length > 0 && this.activeRequests < this.maxConcurrent) {
      const queuedRequest = this.queue.shift()!;
      this.activeRequests++;
      
      this.processRequest(queuedRequest);
      
      if (this.queue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, this.delayBetweenRequests));
      }
    }

    this.processing = false;
  }

  private async processRequest(queuedRequest: QueuedRequest) {
    try {
      const result = await queuedRequest.request();
      queuedRequest.resolve(result);
    } catch (error) {
      if (queuedRequest.retries < queuedRequest.maxRetries) {
        queuedRequest.retries++;
        const delay = Math.min(500 * Math.pow(2, queuedRequest.retries), 5000);
        setTimeout(() => {
          this.queue.unshift(queuedRequest);
          this.processQueue();
        }, delay);
      } else {
        queuedRequest.reject(error);
      }
    } finally {
      this.activeRequests--;
      if (this.queue.length > 0) {
        this.processQueue();
      }
    }
  }
}

export const apiRequestQueue = new RequestQueue();