// Error types for better error handling
export interface APIError extends Error {
  status?: number
  provider?: 'openai' | 'gemini' | 'anthropic' | 'grok'
  retryable?: boolean
  code?: string
}

export interface ProcessingError extends Error {
  type: 'screenshot' | 'audio' | 'validation' | 'config'
  recoverable?: boolean
}

export class APIErrorHandler {
  static handle(error: unknown, provider?: string): { shouldRetry: boolean; delay?: number; message: string } {
    if (this.isAPIError(error)) {
      return this.handleAPIError(error)
    }
    
    return {
      shouldRetry: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }

  private static isAPIError(error: unknown): error is APIError {
    return error instanceof Error && ('status' in error || 'provider' in error)
  }

  private static handleAPIError(error: APIError): { shouldRetry: boolean; delay?: number; message: string } {
    switch (error.status) {
      case 401:
        return { shouldRetry: false, message: `Invalid ${error.provider} API key` }
      case 429:
        return { shouldRetry: true, delay: 60000, message: `${error.provider} rate limit exceeded` }
      case 500:
      case 502:
      case 503:
        return { shouldRetry: true, delay: 5000, message: `${error.provider} server error` }
      default:
        return { shouldRetry: false, message: error.message }
    }
  }
}