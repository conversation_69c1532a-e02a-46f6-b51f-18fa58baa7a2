// ProcessingHelper.ts
import fs from "node:fs"
import path from "node:path"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { IProcessingHelperDeps } from "./main"
import * as axios from "axios"
import { app, <PERSON><PERSON>erWindow, dialog } from "electron"
import { OpenAI } from "openai"
import { configHelper } from "./ConfigHelper"
import Anthropic from '@anthropic-ai/sdk';
import { retryWithBackoff } from '../src/utils/retry';
import { apiRequestQueue } from '../src/utils/requestQueue';
import { APIError, APIErrorHandler } from '../src/types/errors';
import { withErrorHandling, createAPIError } from '../src/utils/errorHandler';

// Interface for Gemini API requests
interface GeminiMessage {
  role: string;
  parts: Array<{
    text?: string;
    inlineData?: {
      mimeType: string;
      data: string;
    }
  }>;
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
  }>;
}
interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: Array<{
    type: 'text' | 'image';
    text?: string;
    source?: {
      type: 'base64';
      media_type: string;
      data: string;
    };
  }>;
}
export class ProcessingHelper {
  private deps: IProcessingHelperDeps
  private screenshotHelper: ScreenshotHelper
  private openaiClient: OpenAI | null = null
  private geminiApiKey: string | null = null
  private anthropicClient: Anthropic | null = null
  private grokClient: OpenAI | null = null

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps
    this.screenshotHelper = deps.getScreenshotHelper()
    
    // Initialize AI client based on config
    this.initializeAIClient();
    
    // Listen for config changes to re-initialize the AI client
    configHelper.on('config-updated', () => {
      this.initializeAIClient();
    });
  }
  
  /**
   * Get extraction prompt based on question detection mode
   */
  private getExtractionPrompt(mode: string, language: string): { system: string; user: string } {
    switch (mode) {
      case "dsa":
        return {
          system: "You are an expert coding interview analyzer. Analyze the screenshot of CODING/DSA problems with 100% accuracy. Look for: function signatures, input/output examples, constraints, algorithm hints, data structure requirements, complexity requirements. Return ONLY valid JSON with these exact fields: problem_statement, constraints, example_input, example_output, question_type, difficulty_level, suggested_approaches. No other text.",
          user: `CODING PROBLEM EXTRACTION - Analyze these screenshots for:

🎯 IDENTIFICATION MARKERS:
- Function signatures (def, function, public static)
- Input/Output examples with arrows (→) or "Output:"
- Constraints (1 ≤ n ≤ 10^5, time limits)
- Return statements or expected outputs
- Algorithm keywords (sort, search, tree, graph, dp)
- Complexity hints (O(n), O(log n))

📋 EXTRACT EXACTLY:
- problem_statement: Complete problem description
- constraints: All numerical/time limits
- example_input: Sample inputs with format
- example_output: Expected outputs
- question_type: "coding" (always for this mode)
- difficulty_level: "easy"/"medium"/"hard"
- suggested_approaches: ["brute_force", "optimized", "advanced"]

Language: ${language}
Return ONLY valid JSON, no markdown, no explanations.`
        };
      
      case "system-design":
        return {
          system: "You are a senior system architect. Analyze screenshots of SYSTEM DESIGN questions with 100% accuracy. Look for: system requirements, user scale, architecture diagrams, component mentions, scalability needs, database requirements, API specifications. Return ONLY valid JSON with these exact fields: problem_statement, functional_requirements, non_functional_requirements, scale_requirements, constraints, question_type, system_components, estimated_scale. No other text.",
          user: `SYSTEM DESIGN EXTRACTION - Analyze these screenshots for:

🏗️ IDENTIFICATION MARKERS:
- "Design a system for...", "Build an architecture..."
- Scale numbers (millions of users, requests/second)
- Component diagrams (boxes, arrows, databases)
- Non-functional requirements (availability, consistency)
- Technology stack mentions (Redis, MongoDB, Kafka)
- API endpoint specifications
- Database schema requirements

📋 EXTRACT EXACTLY:
- problem_statement: What system to design
- functional_requirements: ["feature1", "feature2"]
- non_functional_requirements: {"availability": "99.9%", "latency": "<100ms"}
- scale_requirements: {"users": "10M", "requests_per_second": "100K"}
- constraints: Technical/business limitations
- question_type: "system-design" (always for this mode)
- system_components: ["load_balancer", "database", "cache"]
- estimated_scale: "large"/"medium"/"small"

Return ONLY valid JSON, no markdown, no explanations.`
        };
      
      case "auto-detect":
      default:
        return {
          system: "You are an expert technical interview analyzer with 100% accuracy. First DETECT the question type by analyzing visual cues, then extract accordingly. CODING signs: function signatures, input/output examples, constraints, return statements. SYSTEM DESIGN signs: architecture diagrams, scale requirements, component mentions, 'design a system'. Return ONLY valid JSON with question_type field and appropriate extraction fields. No other text.",
          user: `AUTO-DETECTION ANALYSIS - Follow this exact process:

🔍 STEP 1: DETECT QUESTION TYPE
Look for these EXACT indicators:

CODING/DSA Indicators:
- Function definitions: def, function, public static, class Solution
- Input/Output examples: "Input: [1,2,3]" → "Output: 6"
- Constraints: "1 ≤ n ≤ 10^5", time/space limits
- Return statements, expected outputs
- Algorithm terms: sort, search, tree, graph, dynamic programming
- Code snippets, variable names

SYSTEM DESIGN Indicators:
- "Design a system for...", "Build architecture for..."
- Scale numbers: "10M users", "1000 requests/second"
- Component boxes/diagrams with arrows
- Database/cache/queue mentions
- Non-functional requirements: availability, latency, consistency
- Technology stack: Redis, MongoDB, Kafka, microservices

🎯 STEP 2: EXTRACT BASED ON DETECTED TYPE
If CODING detected → Extract: problem_statement, constraints, example_input, example_output, question_type: "coding"
If SYSTEM DESIGN detected → Extract: problem_statement, functional_requirements, non_functional_requirements, scale_requirements, question_type: "system-design"

⚠️ CONFIDENCE RULE: If unclear, default to "coding" type.

Language preference: ${language}
Return ONLY valid JSON, no markdown, no explanations.`
        };
    }
  }

  /**
   * Check network connectivity
   */
  private async checkNetworkConnection(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch('https://www.google.com/favicon.ico', {
        method: 'HEAD',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Initialize or reinitialize the AI client with current config
   */
  private initializeAIClient(): void {
    try {
      const config = configHelper.loadConfig();
      
      if (config.apiProvider === "openai") {
        const apiKey = configHelper.getCurrentApiKey(config);
        if (apiKey) {
          this.openaiClient = new OpenAI({ 
            apiKey: apiKey,
            timeout: 60000, // 60 second timeout
            maxRetries: 2   // Retry up to 2 times
          });
          this.geminiApiKey = null;
          this.anthropicClient = null;
          this.grokClient = null;
          console.log("OpenAI client initialized successfully");
        } else {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          this.grokClient = null;
          console.warn("No API key available, OpenAI client not initialized");
        }
      } else if (config.apiProvider === "gemini"){
        // Gemini client initialization
        this.openaiClient = null;
        this.anthropicClient = null;
        this.grokClient = null;
        const apiKey = configHelper.getCurrentApiKey(config);
        if (apiKey) {
          this.geminiApiKey = apiKey;
          console.log("Gemini API key set successfully");
        } else {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          this.grokClient = null;
          console.warn("No API key available, Gemini client not initialized");
        }
      } else if (config.apiProvider === "anthropic") {
        // Reset other clients
        this.openaiClient = null;
        this.geminiApiKey = null;
        this.grokClient = null;
        const apiKey = configHelper.getCurrentApiKey(config);
        if (apiKey) {
          this.anthropicClient = new Anthropic({
            apiKey: apiKey,
            timeout: 60000,
            maxRetries: 2
          });
          console.log("Anthropic client initialized successfully");
        } else {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          this.grokClient = null;
          console.warn("No API key available, Anthropic client not initialized");
        }
      } else if (config.apiProvider === "grok") {
        // Reset other clients
        this.openaiClient = null;
        this.geminiApiKey = null;
        this.anthropicClient = null;
        const apiKey = configHelper.getCurrentApiKey(config);
        if (apiKey) {
          this.grokClient = new OpenAI({
            apiKey: apiKey,
            baseURL: "https://api.x.ai/v1",
            timeout: 60000,
            maxRetries: 2
          });
          console.log("Grok client initialized successfully");
        } else {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          this.grokClient = null;
          console.warn("No API key available, Grok client not initialized");
        }
      }
    } catch (error: unknown) {
      console.error("Failed to initialize AI client:", error instanceof Error ? error.message : String(error));
      this.openaiClient = null;
      this.geminiApiKey = null;
      this.anthropicClient = null;
      this.grokClient = null;
    }
  }

  private async waitForInitialization(
    mainWindow: BrowserWindow
  ): Promise<void> {
    let attempts = 0
    const maxAttempts = 50 // 5 seconds total

    while (attempts < maxAttempts) {
      const isInitialized = await mainWindow.webContents.executeJavaScript(
        "window.__IS_INITIALIZED__"
      )
      if (isInitialized) return
      await new Promise((resolve) => setTimeout(resolve, 100))
      attempts++
    }
    throw new Error("App failed to initialize after 5 seconds")
  }

  private async getCredits(): Promise<number> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return 999 // Unlimited credits in this version

    try {
      await this.waitForInitialization(mainWindow)
      return 999 // Always return sufficient credits to work
    } catch (error: unknown) {
      console.error("Error getting credits:", error instanceof Error ? error.message : String(error))
      return 999 // Unlimited credits as fallback
    }
  }

  private async getLanguage(): Promise<string> {
    try {
      // Get language from config
      const config = configHelper.loadConfig();
      if (config.language) {
        return config.language;
      }
      
      // Fallback to window variable if config doesn't have language
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        try {
          await this.waitForInitialization(mainWindow)
          const language = await mainWindow.webContents.executeJavaScript(
            "window.__LANGUAGE__"
          )

          if (
            typeof language === "string" &&
            language !== undefined &&
            language !== null
          ) {
            return language;
          }
        } catch (err) {
          console.warn("Could not get language from window", err);
        }
      }
      
      // Default fallback
      return "python";
    } catch (error: unknown) {
      console.error("Error getting language:", error instanceof Error ? error.message : String(error))
      return "python"
    }
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const config = configHelper.loadConfig();
    
    // First verify we have a valid AI client
    if (config.apiProvider === "openai" && !this.openaiClient) {
      this.initializeAIClient();
      
      if (!this.openaiClient) {
        console.error("OpenAI client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "gemini" && !this.geminiApiKey) {
      this.initializeAIClient();
      
      if (!this.geminiApiKey) {
        console.error("Gemini API key not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "anthropic" && !this.anthropicClient) {
      // Add check for Anthropic client
      this.initializeAIClient();
      
      if (!this.anthropicClient) {
        console.error("Anthropic client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "grok" && !this.grokClient) {
      // Add check for Grok client
      this.initializeAIClient();
      
      if (!this.grokClient) {
        console.error("Grok client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    }

    const view = this.deps.getView()
    console.log("Processing screenshots in view:", view)

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue()
      console.log("Processing main queue screenshots:", screenshotQueue)
      
      // Check if the queue is empty
      if (!screenshotQueue || screenshotQueue.length === 0) {
        console.log("No screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      // Check that files actually exist
      const existingScreenshots = screenshotQueue.filter(path => fs.existsSync(path));
      if (existingScreenshots.length === 0) {
        console.log("Screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      try {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        const screenshots = await Promise.all(
          existingScreenshots.map(async (path) => {
            try {
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);
        
        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data");
        }

        const result = await this.processScreenshotsHelper(validScreenshots, signal)

        if (!result.success) {
          console.log("Processing failed:", result.error)
          if (result.error?.includes("API Key") || result.error?.includes("OpenAI") || result.error?.includes("Gemini")) {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.API_KEY_INVALID
            )
          } else {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
          // DON'T reset view - keep screenshots for retry
          console.log("Processing failed but preserving screenshots for retry")
          return
        }

        // Only set view to solutions if processing succeeded
        console.log("Setting view to solutions after successful processing")
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )
        this.deps.setView("solutions")
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : "Server error. Please try again.";
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          errorMessage
        )
        console.error("Processing error:", errorMessage)
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            errorMessage
          )
        }
        // Reset view back to queue on error
        console.log("Resetting view to queue due to error")
        this.deps.setView("queue")
      } finally {
        this.currentProcessingAbortController = null
      }
    } else {
      // view == 'solutions'
      const extraScreenshotQueue =
        this.screenshotHelper.getExtraScreenshotQueue()
      console.log("Processing extra queue screenshots:", extraScreenshotQueue)
      
      // Check if the extra queue is empty
      if (!extraScreenshotQueue || extraScreenshotQueue.length === 0) {
        console.log("No extra screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        
        return;
      }

      // Check that files actually exist
      const existingExtraScreenshots = extraScreenshotQueue.filter(path => fs.existsSync(path));
      if (existingExtraScreenshots.length === 0) {
        console.log("Extra screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }
      
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START)

      // Initialize AbortController
      this.currentExtraProcessingAbortController = new AbortController()
      const { signal } = this.currentExtraProcessingAbortController

      try {
        // Get all screenshots (both main and extra) for processing
        const allPaths = [
          ...this.screenshotHelper.getScreenshotQueue(),
          ...existingExtraScreenshots
        ];
        
        const screenshots = await Promise.all(
          allPaths.map(async (path) => {
            try {
              if (!fs.existsSync(path)) {
                console.warn(`Screenshot file does not exist: ${path}`);
                return null;
              }
              
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )
        
        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);
        
        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data for debugging");
        }
        
        console.log(
          "Combined screenshots for processing:",
          validScreenshots.map((s) => s.path)
        )

        const result = await this.processExtraScreenshotsHelper(
          validScreenshots,
          signal
        )

        if (result.success) {
          this.deps.setHasDebugged(true)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            result.error
          )
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : "Processing failed";
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Extra processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            errorMessage
          )
        }
      } finally {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  private async processScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const config = configHelper.loadConfig();
      const language = await this.getLanguage();
      const mainWindow = this.deps.getMainWindow();
      
      // Step 1: Extract problem info using AI Vision API (OpenAI or Gemini)
      const imageDataList = screenshots.map(screenshot => screenshot.data);
      
      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Analyzing problem from screenshots...",
          progress: 20
        });
      }

      let problemInfo;
      
      if (config.apiProvider === "openai") {
        // Verify OpenAI client
        if (!this.openaiClient) {
          this.initializeAIClient(); // Try to reinitialize
          
          if (!this.openaiClient) {
            return {
              success: false,
              error: "OpenAI API key not configured or invalid. Please check your settings."
            };
          }
        }

        // Use OpenAI for processing
        const extractionPrompt = this.getExtractionPrompt(config.questionDetectionMode, language);
        
        const messages = [
          {
            role: "system" as const, 
            content: extractionPrompt.system
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const, 
                text: extractionPrompt.user
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        // Send to OpenAI Vision API with timeout
        const extractionResponse = await Promise.race([
          apiRequestQueue.add(() =>
            this.openaiClient!.chat.completions.create({
              model: config.extractionModel || "gpt-4o",
              messages: messages,
              max_tokens: 4000,
              temperature: 0.2
            })
          ),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Request timeout after 30 seconds')), 30000)
          )
        ]) as any;

        // Parse the response with enhanced validation
        try {
          const responseText = extractionResponse.choices[0].message.content;
          console.log("Raw OpenAI response:", responseText);
          
          // Handle when OpenAI might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
          
          // Validate detection confidence
          if (!problemInfo.question_type) {
            problemInfo.question_type = 'coding'; // Default fallback
          }
          
          // Add confidence scoring based on extracted fields
          problemInfo.detection_confidence = this.calculateDetectionConfidence(problemInfo);
          
          console.log("Parsed problem info:", problemInfo);
        } catch (error) {
          console.error("Error parsing OpenAI response:", error);
          return {
            success: false,
            error: "Failed to parse problem information. Please try again or use clearer screenshots."
          };
        }
      } else if (config.apiProvider === "grok") {
        // Use Grok API (OpenAI-compatible)
        if (!this.grokClient) {
          return {
            success: false,
            error: "Grok API key not configured. Please check your settings."
          };
        }

        const extractionPrompt = this.getExtractionPrompt(config.questionDetectionMode, language);
        
        const messages = [
          {
            role: "system" as const, 
            content: extractionPrompt.system
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const, 
                text: extractionPrompt.user
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        try {
          const extractionResponse = await this.grokClient.chat.completions.create({
            model: config.extractionModel || "grok-2-vision-1212",
            messages: messages,
            max_tokens: 4000,
            temperature: 0.2
          });

          const responseText = extractionResponse.choices[0].message.content;
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error: any) {
          console.error("Error using Grok API:", error);
          
          if (error.response?.status === 429) {
            return {
              success: false,
              error: "Grok API rate limit exceeded. Please wait a moment before trying again."
            };
          }
          
          return {
            success: false,
            error: "Failed to process with Grok API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "gemini")  {
        // Use Gemini API
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try {
          // Create Gemini message structure
          const extractionPrompt = this.getExtractionPrompt(config.questionDetectionMode, language);
          
          const geminiMessages: GeminiMessage[] = [
            {
              role: "user",
              parts: [
                {
                  text: `${extractionPrompt.system} ${extractionPrompt.user}`
                },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          // Make API request to Gemini with retry
          const response = await retryWithBackoff(() => 
            axios.default.post(
              `https://generativelanguage.googleapis.com/v1beta/models/${config.extractionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
              {
                contents: geminiMessages,
                generationConfig: {
                  temperature: 0.2,
                  maxOutputTokens: 4000
                }
              },
              { signal }
            )
          );

          const responseData = response.data as GeminiResponse;
          
          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }
          
          const responseText = responseData.candidates[0].content.parts[0].text;
          
          // Handle when Gemini might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error: any) {
          const apiError: APIError = {
            ...error,
            status: error.response?.status,
            provider: 'gemini',
            retryable: error.response?.status === 429
          };
          const handled = APIErrorHandler.handle(apiError, 'gemini');
          return { success: false, error: handled.message };
        }
      } else if (config.apiProvider === "anthropic") {
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try {
          const extractionPrompt = this.getExtractionPrompt(config.questionDetectionMode, language);
          
          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: `${extractionPrompt.system} ${extractionPrompt.user}`
                },
                ...imageDataList.map(data => ({
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const,
                    data: data
                  }
                }))
              ]
            }
          ];

          const response = await this.anthropicClient.messages.create({
            model: config.extractionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          const responseText = (response.content[0] as { type: 'text', text: string }).text;
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error: any) {
          const apiError: APIError = {
            ...error,
            status: error.status,
            provider: 'anthropic',
            retryable: error.status === 429
          };
          const handled = APIErrorHandler.handle(apiError, 'anthropic');
          
          if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Screenshots too large for Claude. Switch to OpenAI or Gemini in settings."
            };
          }
          
          return { success: false, error: handled.message };
        }
      }
      
      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Problem analyzed successfully. Preparing to generate solution...",
          progress: 40
        });
      }

      // Store problem info in AppState
      this.deps.setProblemInfo(problemInfo);

      // Send first success event with confidence info
      if (mainWindow) {
        // Show detection confidence to user
        const confidence = problemInfo.detection_confidence || 0;
        const detectedType = problemInfo.question_type || 'unknown';
        
        mainWindow.webContents.send("processing-status", {
          message: `Detected ${detectedType} question (${confidence}% confidence)`,
          progress: 50
        });
        
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
          problemInfo
        );

        // Generate solutions after successful extraction
        const detectedQuestionType = problemInfo.question_type || 'coding';
        const solutionsResult = detectedQuestionType === 'system-design' || detectedQuestionType === 'architecture' 
          ? await this.generateSystemDesignSolution(signal)
          : await this.generateSolutionsHelper(signal);
          
        if (solutionsResult.success) {
          // Clear any existing extra screenshots before transitioning to solutions view
          this.screenshotHelper.clearExtraScreenshotQueue();
          
          // Final progress update
          mainWindow.webContents.send("processing-status", {
            message: "Solution generated successfully",
            progress: 100
          });
          
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
            solutionsResult.data
          );
          return { success: true, data: solutionsResult.data };
        } else {
          throw new Error(
            solutionsResult.error || "Failed to generate solutions"
          );
        }
      }

      return { success: false, error: "Failed to process screenshots" };
    } catch (error: any) {
      if (axios.isCancel(error)) {
        return { success: false, error: "Processing was canceled by the user." };
      }
      
      const config = configHelper.loadConfig();
      const apiError = createAPIError(error, config?.apiProvider || 'openai');
      const handled = APIErrorHandler.handle(apiError);
      
      if (handled.shouldRetry && error?.response?.status === 500) {
        console.log('Server error, attempting retry...');
        try {
          await new Promise(resolve => setTimeout(resolve, 2000));
          return await this.processScreenshotsHelper(screenshots, signal);
        } catch (retryError) {
          return { success: false, error: "Server error persists. Please try again later." };
        }
      }
      
      return { success: false, error: handled.message };
    }
  }

  private async generateSolutionsHelper(signal: AbortSignal) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Creating optimal solution with detailed explanations...",
          progress: 60
        });
      }

      // Create prompt for generating 3 different solutions with enhanced dry run
      const promptText = `
Generate THREE solutions with DIFFERENT time complexities for this coding problem:

PROBLEM STATEMENT:
${problemInfo.problem_statement}

CONSTRAINTS:
${problemInfo.constraints || "No specific constraints provided."}

EXAMPLE INPUT:
${problemInfo.example_input || "No example input provided."}

EXAMPLE OUTPUT:
${problemInfo.example_output || "No example output provided."}

LANGUAGE: ${language}

IMPORTANT: Each solution MUST have different time complexity:
- Brute Force: O(n²) or higher (nested loops, inefficient approach)
- Less Optimal: O(n log n) (sorting, divide & conquer)
- Most Optimal: O(n) or O(1) (single pass, optimal data structures)

Provide your response in EXACTLY this format:

=== BRUTE FORCE SOLUTION ===
1. QUESTION EXPLANATION:
[Brief explanation of what the problem is asking]

2. APPROACH:
[Explain the brute force approach - use nested loops, check all possibilities, inefficient but simple]

3. ALGORITHM:
[Name and explain the brute force technique - nested iteration, exhaustive search]

4. TIME AND SPACE COMPLEXITY:
Time Complexity: O(n²) - [explain why it's O(n²) with nested loops or repeated operations]
Space Complexity: O(1) or O(n) - [detailed explanation]

5. DRY RUN:
**Input:** ${problemInfo.example_input || "[example input]"}
**Expected Output:** ${problemInfo.example_output || "[expected output]"}

**Step-by-step execution:**
- **Step 1:** [Initialize variables and data structures]
- **Step 2:** [First iteration/operation with specific values]
- **Step 3:** [Second iteration/operation with specific values]
- **Step 4:** [Continue with key steps, showing variable states]
- **Step 5:** [Final step leading to the result]

**Variable states at each step:**
\`\`\`
Step 1: var1=value1, var2=value2
Step 2: var1=value1, var2=value2
...continue for each step
\`\`\`

**Final Result:** [Show how we arrive at the expected output]

6. CODE:
\`\`\`${language}
[Complete brute force solution with comments]
\`\`\`

=== LESS OPTIMAL SOLUTION ===
1. QUESTION EXPLANATION:
[Brief explanation focusing on the improved approach]

2. APPROACH:
[Explain the moderately optimized approach - use sorting, hash maps, or divide & conquer]

3. ALGORITHM:
[Name and explain the technique - sorting algorithm, hash table, binary search]

4. TIME AND SPACE COMPLEXITY:
Time Complexity: O(n log n) - [explain why it's O(n log n) due to sorting or divide & conquer]
Space Complexity: O(n) - [detailed explanation]

5. DRY RUN:
**Input:** ${problemInfo.example_input || "[example input]"}
**Expected Output:** ${problemInfo.example_output || "[expected output]"}

**Step-by-step execution:**
- **Step 1:** [Initialize variables and data structures]
- **Step 2:** [First major operation (sorting/preprocessing)]
- **Step 3:** [Main algorithm execution with specific values]
- **Step 4:** [Continue with key steps, showing data structure states]
- **Step 5:** [Final step leading to the result]

**Data structure states:**
\`\`\`
Initial: [show initial state]
After preprocessing: [show after sorting/setup]
During execution: [show intermediate states]
Final: [show final state]
\`\`\`

**Final Result:** [Show how we arrive at the expected output]

6. CODE:
\`\`\`${language}
[Complete less optimal solution with comments]
\`\`\`

=== MOST OPTIMAL SOLUTION ===
1. QUESTION EXPLANATION:
[Brief explanation focusing on the optimal approach]

2. APPROACH:
[Explain the most efficient approach - single pass, optimal data structure, mathematical formula]

3. ALGORITHM:
[Name and explain the optimal technique - linear scan, two pointers, sliding window]

4. TIME AND SPACE COMPLEXITY:
Time Complexity: O(n) - [explain why it's O(n) with single pass or O(1) if constant time]
Space Complexity: O(1) - [explain constant space usage]

5. DRY RUN:
**Input:** ${problemInfo.example_input || "[example input]"}
**Expected Output:** ${problemInfo.example_output || "[expected output]"}

**Step-by-step execution:**
- **Step 1:** [Initialize minimal variables]
- **Step 2:** [Single pass iteration - first element]
- **Step 3:** [Single pass iteration - second element]
- **Step 4:** [Continue showing how algorithm processes each element]
- **Step 5:** [Final step with optimal result]

**Iteration trace:**
\`\`\`
Element 1: [show processing]
Element 2: [show processing]
...continue for each element
Final state: [show final variables]
\`\`\`

**Final Result:** [Show how we arrive at the expected output in optimal time]

6. CODE:
\`\`\`${language}
[Complete optimal solution with comments]
\`\`\`

Make sure to follow this exact format and structure with detailed dry runs.
`;

      let responseContent;
      
      if (config.apiProvider === "openai") {
        // OpenAI processing
        if (!this.openaiClient) {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }
        
        // Send to OpenAI API
        const solutionResponse = await apiRequestQueue.add(() =>
          this.openaiClient!.chat.completions.create({
            model: config.solutionModel || "gpt-4o",
            messages: [
              { role: "system", content: "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations." },
              { role: "user", content: promptText }
            ],
            max_tokens: 4000,
            temperature: 0.2
          }));

        responseContent = solutionResponse.choices[0].message.content;
      } else if (config.apiProvider === "grok") {
        // Grok processing
        if (!this.grokClient) {
          return {
            success: false,
            error: "Grok API key not configured. Please check your settings."
          };
        }
        
        // Send to Grok API
        const solutionResponse = await this.grokClient.chat.completions.create({
          model: config.solutionModel || "grok-3",
          messages: [
            { role: "system", content: "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations." },
            { role: "user", content: promptText }
          ],
          max_tokens: 4000,
          temperature: 0.2
        });

        responseContent = solutionResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini")  {
        // Gemini processing
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }
        
        try {
          // Create Gemini message structure
          const geminiMessages = [
            {
              role: "user",
              parts: [
                {
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}`
                }
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.solutionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;
          
          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }
          
          responseContent = responseData.candidates[0].content.parts[0].text;
        } catch (error) {
          console.error("Error using Gemini API for solution:", error);
          return {
            success: false,
            error: "Failed to generate solution with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        // Anthropic processing
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }
        
        try {
          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}`
                }
              ]
            }
          ];

          // Send to Anthropic API
          const response = await this.anthropicClient.messages.create({
            model: config.solutionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          responseContent = (response.content[0] as { type: 'text', text: string }).text;
        } catch (error: any) {
          console.error("Error using Anthropic API for solution:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to generate solution with Anthropic API. Please check your API key or try again later."
          };
        }
      }
      
      // Parse the three solutions from response
      const solutions = this.parseThreeSolutions(responseContent, language);
      
      const formattedResponse = {
        solutions: solutions,
        selectedSolution: 'brute_force' // Default selection
      };

      return { success: true, data: formattedResponse };
    } catch (error: any) {
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }
      
      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      }
      
      console.error("Solution generation error:", error);
      return { success: false, error: error.message || "Failed to generate solution" };
    }
  }

  private async generateSystemDesignSolution(signal: AbortSignal) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Designing system architecture with code examples...",
          progress: 60
        });
      }

      const promptText = `Design THREE DIFFERENT ARCHITECTURES for: "${problemInfo.problem_statement}"

Requirements: ${JSON.stringify(problemInfo.functional_requirements || [])}
Scale: ${JSON.stringify(problemInfo.scale_requirements || {})}

Provide THREE COMPLEXITY LEVELS with specific architectures:

=== BASIC ARCHITECTURE (< 1K users) ===
# 🏗️ MONOLITHIC DESIGN
## Simple Architecture
- Single application server
- One database instance
- Basic load balancer
- Minimal caching

## Implementation
\`\`\`python
# Simple Flask/Django monolith
\`\`\`

## Capacity Planning
- Users: < 1,000
- Requests/sec: < 100
- Database: Single MySQL instance
- Cost: $50-200/month

=== SCALABLE ARCHITECTURE (1K-100K users) ===
# 🔧 MICROSERVICES DESIGN
## Distributed Architecture
- Multiple microservices
- Database per service
- API Gateway
- Redis caching
- Message queues

## Implementation
\`\`\`python
# Microservices with Docker/Kubernetes
\`\`\`

## Capacity Planning
- Users: 1K - 100K
- Requests/sec: 100 - 10K
- Database: Sharded MySQL/PostgreSQL
- Cost: $500-5K/month

=== ENTERPRISE ARCHITECTURE (100K+ users) ===
# ⚡ EVENT-DRIVEN DESIGN
## Global Scale Architecture
- Event-driven microservices
- Multi-region deployment
- CQRS + Event Sourcing
- Advanced caching strategies
- Auto-scaling infrastructure

## Implementation
\`\`\`python
# Event-driven with Kafka, CQRS
\`\`\`

## Capacity Planning
- Users: 100K+
- Requests/sec: 10K+
- Database: Multi-region NoSQL + SQL
- Cost: $5K+/month

Provide STEP-BY-STEP system design with detailed explanations:

# 🏗️ STEP 1: HIGH LEVEL DESIGN (HLD)

## 1.1 System Architecture Overview
**Explain the overall architecture approach and why it's chosen**
- Architecture pattern (microservices/monolith/serverless)
- Key design principles applied
- Technology stack rationale

## 1.2 Core Components Breakdown
**List and explain each major component:**
- Load Balancer: Purpose and configuration
- API Gateway: Routing and rate limiting
- Application Services: Business logic separation
- Database Layer: Data storage strategy
- Caching Layer: Performance optimization
- Message Queue: Async processing

## 1.3 Data Flow Diagram
**Step-by-step request flow:**
\`\`\`
Step 1: Client Request → Load Balancer
Step 2: Load Balancer → API Gateway
Step 3: API Gateway → Auth Service (validation)
Step 4: Auth Service → Business Service
Step 5: Business Service → Database
Step 6: Response flows back through same path
\`\`\`

## 1.4 Scalability Strategy
**Horizontal scaling approach:**
- Auto-scaling triggers and thresholds
- Database sharding strategy
- Caching layers (Redis/Memcached)
- CDN for static content

# 🔧 STEP 2: LOW LEVEL DESIGN (LLD)

## 2.1 Database Schema Design
**Complete table structures with relationships:**
\`\`\`sql
-- Users table with indexes
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email)
);

-- Add more tables with proper relationships
\`\`\`

## 2.2 API Specifications
**RESTful endpoints with complete details:**
\`\`\`
POST /api/v1/users
Request: {"username": "john", "email": "<EMAIL>", "password": "***"}
Response: {"id": 123, "username": "john", "created_at": "2024-01-01T00:00:00Z"}
Status Codes: 201 (Created), 400 (Bad Request), 409 (Conflict)

GET /api/v1/users/{id}
Response: {"id": 123, "username": "john", "email": "<EMAIL>"}
Status Codes: 200 (OK), 404 (Not Found)
\`\`\`

## 2.3 Core Classes Implementation
**Main service classes with business logic:**
\`\`\`python
class UserService:
    def __init__(self, db_connection, cache_client):
        self.db = db_connection
        self.cache = cache_client
    
    def create_user(self, username, email, password):
        # Validation logic
        if self.user_exists(email):
            raise UserAlreadyExistsError()
        
        # Hash password
        password_hash = bcrypt.hashpw(password.encode(), bcrypt.gensalt())
        
        # Database insertion
        user_id = self.db.execute(
            "INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)",
            (username, email, password_hash)
        )
        
        # Cache the user
        user_data = {"id": user_id, "username": username, "email": email}
        self.cache.set(f"user:{user_id}", json.dumps(user_data), ttl=3600)
        
        return user_data
    
    def get_user(self, user_id):
        # Try cache first
        cached = self.cache.get(f"user:{user_id}")
        if cached:
            return json.loads(cached)
        
        # Fallback to database
        user = self.db.fetch_one(
            "SELECT id, username, email FROM users WHERE id = ?", 
            (user_id,)
        )
        
        if user:
            # Update cache
            self.cache.set(f"user:{user_id}", json.dumps(user), ttl=3600)
        
        return user
\`\`\`

# 📊 STEP 3: SYSTEM DIAGRAMS

## 3.1 Architecture Diagram
\`\`\`
                    [Internet]
                         |
                  [Load Balancer]
                    /    |    \\
            [Server1] [Server2] [Server3]
                 |        |        |
              [API Gateway] [API Gateway] [API Gateway]
                 |        |        |
            [Auth Service] [User Service] [Order Service]
                 |        |        |
                  \\      |       /
                   [Database Cluster]
                         |
                   [Redis Cache]
\`\`\`

## 3.2 Database ER Diagram
\`\`\`
Users ||--o{ Orders : "user_id"
Orders ||--o{ OrderItems : "order_id"
Products ||--o{ OrderItems : "product_id"
Users ||--o{ Reviews : "user_id"
Products ||--o{ Reviews : "product_id"
\`\`\`

## 3.3 Sequence Diagram for User Creation
\`\`\`
Client -> API Gateway: POST /users
API Gateway -> Auth Service: validate_request()
Auth Service -> API Gateway: request_valid
API Gateway -> User Service: create_user(data)
User Service -> Database: INSERT user
Database -> User Service: user_id
User Service -> Cache: store_user(user_data)
User Service -> API Gateway: user_created
API Gateway -> Client: 201 Created
\`\`\`

# ⚡ STEP 4: PERFORMANCE & SCALABILITY

## 4.1 Bottleneck Analysis
**Identify potential bottlenecks:**
- Database connection limits
- Memory usage in cache layer
- Network bandwidth for file uploads
- CPU usage during peak hours

## 4.2 Optimization Strategies
**Specific optimization techniques:**
- Database query optimization with proper indexes
- Connection pooling (max 100 connections per service)
- Async processing for heavy operations
- Image compression and CDN usage

## 4.3 Monitoring Setup
**Key metrics to track:**
- Response time: < 200ms for 95th percentile
- Error rate: < 0.1%
- Database connection usage: < 80%
- Cache hit ratio: > 90%

# 🔒 STEP 5: SECURITY IMPLEMENTATION

## 5.1 Authentication Flow
\`\`\`python
def authenticate_user(token):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
        user_id = payload['user_id']
        
        # Check token blacklist
        if redis_client.get(f"blacklist:{token}"):
            raise InvalidTokenError()
        
        return user_id
    except jwt.ExpiredSignatureError:
        raise TokenExpiredError()
\`\`\`

## 5.2 Data Encryption
- Passwords: bcrypt with salt rounds = 12
- API tokens: JWT with 24-hour expiry
- Database: AES-256 encryption for sensitive fields
- Transit: TLS 1.3 for all communications

Format with proper markdown, code blocks, and step-by-step explanations.`;

      let responseContent;
      
      if (config.apiProvider === "openai") {
        if (!this.openaiClient) {
          return { success: false, error: "OpenAI API key not configured." };
        }
        
        const solutionResponse = await this.openaiClient.chat.completions.create({
          model: config.solutionModel || "gpt-4o",
          messages: [
            { role: "system", content: "You are a senior system architect. Provide comprehensive system design solutions with practical code examples." },
            { role: "user", content: promptText }
          ],
          max_tokens: 4000,
          temperature: 0.2
        });

        responseContent = solutionResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini") {
        if (!this.geminiApiKey) {
          return { success: false, error: "Gemini API key not configured." };
        }
        
        const geminiMessages = [{
          role: "user",
          parts: [{ text: `You are a senior system architect. ${promptText}` }]
        }];

        const response = await axios.default.post(
          `https://generativelanguage.googleapis.com/v1beta/models/${config.solutionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
          {
            contents: geminiMessages,
            generationConfig: { temperature: 0.2, maxOutputTokens: 4000 }
          },
          { signal }
        );

        responseContent = response.data.candidates[0].content.parts[0].text;
      } else {
        return { success: false, error: "System design requires OpenAI or Gemini API." };
      }
      
      // Parse system design response with tiered complexity
      const systemDesignSolution = {
        solutions: {
          brute_force: {
            code: this.extractCodeFromSystemDesign(responseContent, 'basic'),
            thoughts: this.extractSystemDesignThoughts(responseContent, 'basic'),
            time_complexity: "Basic Architecture - Monolithic",
            space_complexity: "Single Database - Limited Scale",
            scale: "< 1K users",
            architecture: "Monolithic"
          },
          less_optimal: {
            code: this.extractCodeFromSystemDesign(responseContent, 'scalable'),
            thoughts: this.extractSystemDesignThoughts(responseContent, 'scalable'),
            time_complexity: "Microservices - Horizontal Scale", 
            space_complexity: "Distributed Database - Medium Scale",
            scale: "1K - 100K users",
            architecture: "Microservices"
          },
          most_optimal: {
            code: this.extractCodeFromSystemDesign(responseContent, 'enterprise'),
            thoughts: this.extractSystemDesignThoughts(responseContent, 'enterprise'),
            time_complexity: "Event-Driven - Global Scale",
            space_complexity: "Multi-Region - Unlimited Scale",
            scale: "100K+ users",
            architecture: "Event-Driven"
          }
        },
        selectedSolution: 'brute_force',
        isSystemDesign: true,
        fullResponse: responseContent,
        capacityPlanning: this.extractCapacityPlanning(responseContent),
        techStack: this.extractTechStack(responseContent)
      };

      return { success: true, data: systemDesignSolution };
    } catch (error: any) {
      console.error("System design generation error:", error);
      return { success: false, error: error.message || "Failed to generate system design" };
    }
  }

  private extractCodeFromSystemDesign(content: string, complexity?: string): string {
    if (complexity) {
      const complexityMap = {
        'basic': 'BASIC ARCHITECTURE',
        'scalable': 'SCALABLE ARCHITECTURE', 
        'enterprise': 'ENTERPRISE ARCHITECTURE'
      };
      const section = this.extractSection(content, `=== ${complexityMap[complexity]}`, '===');
      const codeBlocks = section.match(/```[\s\S]*?```/g) || [];
      return codeBlocks.join('\n\n') || `// ${complexity} architecture code examples`;
    }
    const codeBlocks = content.match(/```[\s\S]*?```/g) || [];
    return codeBlocks.join('\n\n') || '// System design code examples not found';
  }

  private extractSystemDesignThoughts(content: string, complexity?: string): string[] {
    if (complexity) {
      const complexityMap = {
        'basic': 'BASIC ARCHITECTURE',
        'scalable': 'SCALABLE ARCHITECTURE',
        'enterprise': 'ENTERPRISE ARCHITECTURE'
      };
      const section = this.extractSection(content, `=== ${complexityMap[complexity]}`, '===');
      const thoughts = [
        this.extractSection(section, '## Simple Architecture', '## Implementation'),
        this.extractSection(section, '## Distributed Architecture', '## Implementation'),
        this.extractSection(section, '## Global Scale Architecture', '## Implementation'),
        this.extractSection(section, '## Capacity Planning', '')
      ].filter(section => section.length > 10);
      return thoughts.length > 0 ? thoughts : [`${complexity} architecture analysis`];
    }
    
    const sections = [
      this.extractSection(content, '## 🏗️ HIGH LEVEL DESIGN', '## 🔧 LOW LEVEL DESIGN'),
      this.extractSection(content, '## 🔧 LOW LEVEL DESIGN', '## 💻 CODE EXAMPLES'),
      this.extractSection(content, '## 📊 SYSTEM DIAGRAMS', '## ⚡ PERFORMANCE'),
      this.extractSection(content, '## ⚡ PERFORMANCE & SCALABILITY', '## 🔒 SECURITY')
    ].filter(section => section.length > 10);
    
    return sections.length > 0 ? sections : ['System design analysis completed'];
  }

  private extractCapacityPlanning(content: string): any {
    const capacitySection = this.extractSection(content, '## Capacity Planning', '##');
    const users = capacitySection.match(/Users:\s*([^\n]+)/)?.[1] || 'Not specified';
    const rps = capacitySection.match(/Requests\/sec:\s*([^\n]+)/)?.[1] || 'Not specified';
    const cost = capacitySection.match(/Cost:\s*([^\n]+)/)?.[1] || 'Not specified';
    
    return { users, requestsPerSecond: rps, estimatedCost: cost };
  }

  private extractTechStack(content: string): string[] {
    const techMatches = content.match(/(MySQL|PostgreSQL|Redis|Kafka|Docker|Kubernetes|MongoDB|Cassandra|Elasticsearch|RabbitMQ|AWS|GCP|Azure)/gi) || [];
    return [...new Set(techMatches.map(tech => tech.toLowerCase()))];
  }

  /**
   * Parse three solutions from AI response
   */
  private parseThreeSolutions(content: string, language: string) {
    const solutions = {
      brute_force: this.parseSingleSolution(content, '=== BRUTE FORCE SOLUTION ===', '=== LESS OPTIMAL SOLUTION ==='),
      less_optimal: this.parseSingleSolution(content, '=== LESS OPTIMAL SOLUTION ===', '=== MOST OPTIMAL SOLUTION ==='),
      most_optimal: this.parseSingleSolution(content, '=== MOST OPTIMAL SOLUTION ===', null)
    };
    
    return solutions;
  }
  
  /**
   * Parse a single solution section with enhanced dry run parsing
   */
  private parseSingleSolution(content: string, startMarker: string, endMarker: string | null) {
    const solutionContent = this.extractSection(content, startMarker, endMarker);
    
    if (!solutionContent || solutionContent.length < 50) {
      // FALLBACK: Try to extract any code block from the content
      const fallbackCode = this.extractAnyCodeBlock(content);
      return {
        code: fallbackCode || `// No content found for ${startMarker}`,
        thoughts: [`Failed to parse ${startMarker} - using fallback`],
        time_complexity: "N/A",
        space_complexity: "N/A"
      };
    }
    
    const sections = {
      question: this.extractSection(solutionContent, '1. QUESTION EXPLANATION:', '2. APPROACH:'),
      approach: this.extractSection(solutionContent, '2. APPROACH:', '3. ALGORITHM:'),
      algorithm: this.extractSection(solutionContent, '3. ALGORITHM:', '4. TIME AND SPACE COMPLEXITY:'),
      complexity: this.extractSection(solutionContent, '4. TIME AND SPACE COMPLEXITY:', '5. DRY RUN:'),
      dryRun: this.extractSection(solutionContent, '5. DRY RUN:', '6. CODE:'),
      code: this.extractSection(solutionContent, '6. CODE:', null)
    };
    
    // ENHANCED: Multiple fallback strategies for code extraction
    let code = '';
    
    // Strategy 1: Extract from proper code section
    const codeMatch = sections.code.match(/```(?:\w+)?\s*([\s\S]*?)```/);
    if (codeMatch && codeMatch[1].trim()) {
      code = codeMatch[1].trim();
    }
    
    // Strategy 2: If no code in section, search entire solution content
    if (!code) {
      const fallbackMatch = solutionContent.match(/```(?:\w+)?\s*([\s\S]*?)```/);
      if (fallbackMatch && fallbackMatch[1].trim()) {
        code = fallbackMatch[1].trim();
      }
    }
    
    // Strategy 3: Use raw code section if nothing else works
    if (!code && sections.code.trim()) {
      code = sections.code.trim();
    }
    
    // Parse complexity with better regex
    const timeComplexityMatch = sections.complexity.match(/Time Complexity:\s*([^\n\r]+)/i);
    const spaceComplexityMatch = sections.complexity.match(/Space Complexity:\s*([^\n\r]+)/i);
    
    const timeComplexity = timeComplexityMatch ? timeComplexityMatch[1].trim() : "O(n)";
    const spaceComplexity = spaceComplexityMatch ? spaceComplexityMatch[1].trim() : "O(1)";
    
    // Enhanced thoughts array with better dry run formatting
    const thoughts = [
      `**Question:** ${sections.question.trim()}`,
      `**Approach:** ${sections.approach.trim()}`,
      `**Algorithm:** ${sections.algorithm.trim()}`,
      `**Dry Run:**\n${sections.dryRun.trim()}`
    ].filter(thought => !thought.endsWith(': ') && thought.length > 15);
    
    return {
      code: code || `// No code found for ${startMarker}`,
      thoughts: thoughts.length > 0 ? thoughts : [`Analysis for ${startMarker}`],
      time_complexity: timeComplexity,
      space_complexity: spaceComplexity
    };
  }
  
  /**
   * Calculate detection confidence based on extracted fields
   */
  private calculateDetectionConfidence(problemInfo: any): number {
    let confidence = 0;
    
    if (problemInfo.question_type === 'coding') {
      // Coding indicators
      if (problemInfo.example_input && problemInfo.example_output) confidence += 30;
      if (problemInfo.constraints && problemInfo.constraints.length > 0) confidence += 25;
      if (problemInfo.problem_statement && problemInfo.problem_statement.includes('return')) confidence += 20;
      if (problemInfo.suggested_approaches && problemInfo.suggested_approaches.length > 0) confidence += 15;
      if (problemInfo.difficulty_level) confidence += 10;
    } else if (problemInfo.question_type === 'system-design') {
      // System design indicators
      if (problemInfo.functional_requirements && problemInfo.functional_requirements.length > 0) confidence += 30;
      if (problemInfo.scale_requirements && Object.keys(problemInfo.scale_requirements).length > 0) confidence += 25;
      if (problemInfo.system_components && problemInfo.system_components.length > 0) confidence += 20;
      if (problemInfo.non_functional_requirements) confidence += 15;
      if (problemInfo.estimated_scale) confidence += 10;
    }
    
    return Math.min(100, confidence);
  }

  /**
   * Helper method to extract sections from formatted response
   */
  private extractSection(content: string, startMarker: string, endMarker: string | null): string {
    const startIndex = content.indexOf(startMarker);
    if (startIndex === -1) return '';
    
    const contentStart = startIndex + startMarker.length;
    
    if (endMarker === null || endMarker === '') {
      return content.substring(contentStart).trim();
    }
    
    const endIndex = content.indexOf(endMarker, contentStart);
    if (endIndex === -1) {
      return content.substring(contentStart).trim();
    }
    
    return content.substring(contentStart, endIndex).trim();
  }

  /**
   * Extract any code block from content as fallback
   */
  private extractAnyCodeBlock(content: string): string {
    // Try to find any code block
    const codeBlocks = content.match(/```[\s\S]*?```/g);
    if (codeBlocks && codeBlocks.length > 0) {
      // Extract content from the first code block
      const match = codeBlocks[0].match(/```(?:\w+)?\s*([\s\S]*?)```/);
      if (match && match[1].trim()) {
        return match[1].trim();
      }
    }
    
    // Fallback: Look for function-like patterns
    const functionPatterns = [
      /def\s+\w+[\s\S]*?(?=\n\n|def\s|class\s|$)/g,
      /function\s+\w+[\s\S]*?(?=\n\n|function\s|$)/g,
      /class\s+\w+[\s\S]*?(?=\n\n|class\s|$)/g
    ];
    
    for (const pattern of functionPatterns) {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        return matches.join('\n\n');
      }
    }
    
    return '';
  }

  private async processExtraScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Processing debug screenshots...",
          progress: 30
        });
      }

      // Prepare the images for the API call
      const imageDataList = screenshots.map(screenshot => screenshot.data);
      
      let debugContent;
      
      if (config.apiProvider === "openai") {
        if (!this.openaiClient) {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }
        
        const messages = [
          {
            role: "system" as const, 
            content: `You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

Your response MUST follow this exact structure with these section headers (use ### for headers):
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).`
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const, 
                text: `I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution. Here are screenshots of my code, the errors or test cases. Please provide a detailed analysis with:
1. What issues you found in my code
2. Specific improvements and corrections
3. Any optimizations that would make the solution better
4. A clear explanation of the changes needed` 
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        if (mainWindow) {
          mainWindow.webContents.send("processing-status", {
            message: "Analyzing code and generating debug feedback...",
            progress: 60
          });
        }

        const debugResponse = await apiRequestQueue.add(() =>
          this.openaiClient!.chat.completions.create({
            model: config.debuggingModel || "gpt-4o",
            messages: messages,
            max_tokens: 4000,
            temperature: 0.2
          }));
        
        debugContent = debugResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini")  {
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }
        
        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).
`;

          const geminiMessages = [
            {
              role: "user",
              parts: [
                { text: debugPrompt },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Gemini...",
              progress: 60
            });
          }

          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.debuggingModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;
          
          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }
          
          debugContent = responseData.candidates[0].content.parts[0].text;
        } catch (error) {
          console.error("Error using Gemini API for debugging:", error);
          return {
            success: false,
            error: "Failed to process debug request with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }
        
        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification.
`;

          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: debugPrompt
                },
                ...imageDataList.map(data => ({
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const, 
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Claude...",
              progress: 60
            });
          }

          const response = await this.anthropicClient.messages.create({
            model: config.debuggingModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });
          
          debugContent = (response.content[0] as { type: 'text', text: string }).text;
        } catch (error: any) {
          console.error("Error using Anthropic API for debugging:", error);
          
          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }
          
          return {
            success: false,
            error: "Failed to process debug request with Anthropic API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "grok") {
        if (!this.grokClient) {
          return {
            success: false,
            error: "Grok API key not configured. Please check your settings."
          };
        }
        
        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification.
`;

          const messages = [
            {
              role: "system" as const, 
              content: "You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help."
            },
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const, 
                  text: debugPrompt
                },
                ...imageDataList.map(data => ({
                  type: "image_url" as const,
                  image_url: { url: `data:image/png;base64,${data}` }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Grok...",
              progress: 60
            });
          }

          const response = await this.grokClient.chat.completions.create({
            model: config.debuggingModel || "grok-3",
            messages: messages,
            max_tokens: 4000,
            temperature: 0.2
          });
          
          debugContent = response.choices[0].message.content;
        } catch (error: any) {
          console.error("Error using Grok API for debugging:", error);
          
          if (error.response?.status === 429) {
            return {
              success: false,
              error: "Grok API rate limit exceeded. Please wait a moment before trying again."
            };
          }
          
          return {
            success: false,
            error: "Failed to process debug request with Grok API. Please check your API key or try again later."
          };
        }
      }
      
      
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Debug analysis complete",
          progress: 100
        });
      }

      let extractedCode = "// Debug mode - see analysis below";
      const codeMatch = debugContent.match(/```(?:[a-zA-Z]+)?([\s\S]*?)```/);
      if (codeMatch && codeMatch[1]) {
        extractedCode = codeMatch[1].trim();
      }

      let formattedDebugContent = debugContent;
      
      if (!debugContent.includes('# ') && !debugContent.includes('## ')) {
        formattedDebugContent = debugContent
          .replace(/issues identified|problems found|bugs found/i, '## Issues Identified')
          .replace(/code improvements|improvements|suggested changes/i, '## Code Improvements')
          .replace(/optimizations|performance improvements/i, '## Optimizations')
          .replace(/explanation|detailed analysis/i, '## Explanation');
      }

      const bulletPoints = formattedDebugContent.match(/(?:^|\n)[ ]*(?:[-*•]|\d+\.)[ ]+([^\n]+)/g);
      const thoughts = bulletPoints 
        ? bulletPoints.map(point => point.replace(/^[ ]*(?:[-*•]|\d+\.)[ ]+/, '').trim()).slice(0, 5)
        : ["Debug analysis based on your screenshots"];
      
      const response = {
        code: extractedCode,
        debug_analysis: formattedDebugContent,
        thoughts: thoughts,
        time_complexity: "N/A - Debug mode",
        space_complexity: "N/A - Debug mode"
      };

      return { success: true, data: response };
    } catch (error: any) {
      console.error("Debug processing error:", error);
      return { success: false, error: error.message || "Failed to process debug request" };
    }
  }

  public async processAudioBase64(data: string, mimeType: string): Promise<{ 
    transcription: string; 
    isQuestion: boolean; 
    questionType?: string;
    response?: string;
    timestamp: number 
  }> {
    const config = configHelper.loadConfig();
    console.log('Processing audio with provider:', config.apiProvider);
    
    if (config.apiProvider === "gemini" && this.geminiApiKey) {
      try {
        console.log('Using Gemini for audio processing');
        
        // Enhanced request for better audio processing
        const response = await axios.default.post(
          `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${this.geminiApiKey}`,
          {
            contents: [{
              role: "user",
              parts: [
                { 
                  text: `IMPORTANT: This audio may contain quiet/slow speech from an interviewer. Please transcribe carefully and detect ANY technical question, even if spoken softly or slowly.

Transcribe this audio and analyze if it's a technical question. If it's a question, provide a comprehensive response using markdown formatting.

Respond in this EXACT format:

TRANSCRIPTION: [exact transcribed text - be very careful with quiet/slow speech]
IS_QUESTION: [true/false - err on the side of detecting questions]
QUESTION_TYPE: [coding/system-design/database/networking/security/devops/architecture/behavioral-tech/debugging/explanation/other]
RESPONSE: [if question, provide detailed markdown response with:

## 🎯 Concept Explanation
- Key definitions and terminology
- Fundamental principles

## 🚀 Approach & Methodology  
- Step-by-step solution approach
- Multiple approaches if applicable

## 💻 Implementation
- Code examples in proper code blocks
- Architecture details
- Technical specifications

## ⚡ Best Practices
- Industry standards
- Common pitfalls to avoid
- Performance tips

## 🌍 Real-World Examples
- Practical applications
- Industry use cases

Use proper markdown: headers (##), lists (-), code blocks (\`\`\`), emphasis (**bold**), etc.]

DETECT ANY TECHNICAL QUESTION even if spoken quietly, slowly, or with pauses. Provide thorough, interview-ready answers.` 
                },
                { inlineData: { mimeType, data } }
              ]
            }],
            generationConfig: {
              temperature: 0.1,
              maxOutputTokens: 1500,
              topP: 0.8,
              topK: 40
            }
          }
        );
        
        const responseData = response.data as GeminiResponse;
        if (!responseData.candidates || responseData.candidates.length === 0) {
          throw new Error("Empty response from Gemini API");
        }
        
        const responseText = responseData.candidates[0].content.parts[0].text;
        console.log('Gemini response:', responseText);
        
        // Parse JSON response with better error handling
        let result = {
          transcription: "Audio processed",
          isQuestion: false,
          questionType: null,
          response: null
        };
        
        try {
          // Parse structured response format
          const transcriptionMatch = responseText.match(/TRANSCRIPTION:\s*(.+?)(?=\nIS_QUESTION:|$)/s);
          const isQuestionMatch = responseText.match(/IS_QUESTION:\s*(true|false)/i);
          const questionTypeMatch = responseText.match(/QUESTION_TYPE:\s*(\w+(?:-\w+)?)/i);
          const responseMatch = responseText.match(/RESPONSE:\s*(.+)$/s);
          
          if (transcriptionMatch) {
            result.transcription = transcriptionMatch[1].trim();
          }
          
          if (isQuestionMatch) {
            result.isQuestion = isQuestionMatch[1].toLowerCase() === 'true';
          }
          
          if (questionTypeMatch) {
            result.questionType = questionTypeMatch[1].toLowerCase();
          }
          
          if (responseMatch && result.isQuestion) {
            result.response = responseMatch[1].trim();
          }
          
          // Fallback if structured format not found
          if (!transcriptionMatch) {
            result.transcription = responseText.split('\n')[0] || responseText.substring(0, 200);
            result.isQuestion = responseText.toLowerCase().includes('question') || responseText.includes('?');
            result.questionType = 'explanation';
            result.response = responseText;
          }
        } catch (parseError) {
          console.warn("Response parsing failed, using fallback:", parseError);
          result.transcription = responseText.substring(0, 200);
          result.isQuestion = true;
          result.questionType = 'explanation';
          result.response = responseText;
        }
        
        return {
          transcription: result.transcription,
          isQuestion: result.isQuestion,
          questionType: result.questionType,
          response: result.response,
          timestamp: Date.now()
        };
        
      } catch (error: any) {
        const apiError: APIError = {
          ...error,
          status: error.response?.status,
          provider: 'gemini',
          retryable: error.response?.status === 429
        };
        const handled = APIErrorHandler.handle(apiError, 'gemini');
        throw new Error(handled.message);
      }
    }
    
    // Add OpenAI Whisper support for transcription
    if (config.apiProvider === "openai" && this.openaiClient) {
      try {
        // Convert base64 to buffer for OpenAI Whisper
        const audioBuffer = Buffer.from(data, 'base64');
        const tmp = require('tmp');
        const tempFile = tmp.tmpNameSync({ postfix: '.wav' });
        fs.writeFileSync(tempFile, audioBuffer);
        
        // Enhanced Whisper transcription for quiet audio
        const transcription = await this.openaiClient.audio.transcriptions.create({
          file: fs.createReadStream(tempFile),
          model: 'whisper-1',
          prompt: 'This is a technical interview question that may be spoken quietly or slowly. Please transcribe carefully.',
          temperature: 0.0,
          language: 'en'
        });
        
        // Clean up temp file
        try {
          fs.unlinkSync(tempFile);
        } catch (cleanupErr: unknown) {
          console.warn("Failed to clean up temp file:", cleanupErr instanceof Error ? cleanupErr.message : String(cleanupErr));
        }
        
        const transcribedText = transcription.text.trim();
        
        // Analyze question with GPT
        const analysisResponse = await this.openaiClient.chat.completions.create({
          model: config.extractionModel || "gpt-4o",
          messages: [{
            role: "user",
            content: `Analyze if this is ANY technical interview question and respond in JSON:
{"isQuestion": boolean, "questionType": "coding"|"system-design"|"database"|"networking"|"security"|"devops"|"architecture"|"behavioral-tech"|"debugging"|"explanation"|"other"|null}

Detect technical questions about: coding, algorithms, system design, databases, networking, security, DevOps, cloud, architecture, technical behavioral questions, debugging, etc.

Text: "${transcribedText}"`
          }],
          temperature: 0.2,
          max_tokens: 100
        });
        
        let analysis = { isQuestion: false, questionType: null };
        try {
          const analysisText = analysisResponse.choices[0].message.content;
          const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            analysis = JSON.parse(jsonMatch[0]);
          }
        } catch (parseError) {
          console.warn("Failed to parse OpenAI question analysis:", parseError);
        }
        
        let response = undefined;
        if (analysis.isQuestion) {
          const language = await this.getLanguage();
          const responseGeneration = await this.openaiClient.chat.completions.create({
            model: config.solutionModel || "gpt-4o",
            messages: [{
              role: "user",
              content: `Technical interviewer asked: "${transcribedText}"

Provide a comprehensive, in-depth response for this ${analysis.questionType} question using proper markdown formatting:

## 🎯 Concept Explanation
- Define key concepts and terminology
- Explain the fundamental principles  
- Provide context and background

## 🚀 Detailed Approach
- Step-by-step methodology
- Multiple solution approaches if applicable
- Trade-offs between different approaches

## 💻 Implementation Details
${analysis.questionType === 'coding' ? `- Complete code solution in ${language} with detailed comments
- Time and space complexity analysis
- Edge cases and error handling
- Alternative implementations

Use code blocks with language specification:
\`\`\`${language}
// Your code here
\`\`\`` : ''}
${analysis.questionType === 'system-design' ? `- Detailed architecture breakdown
- Component interactions and data flow
- Scalability considerations
- Technology stack recommendations
- Database design and caching strategies` : ''}
${analysis.questionType === 'database' ? `- Schema design with relationships
- Query optimization techniques
- Indexing strategies
- Performance considerations` : ''}

## ⚡ Best Practices & Considerations
- Industry standards and conventions
- Common pitfalls to avoid
- Performance optimization tips
- Security considerations
- Maintenance and testing strategies

## 🌍 Real-World Examples
- Practical use cases
- How major companies solve this
- Industry applications

> **Note:** Use proper markdown formatting with headers, lists, code blocks, and emphasis for better readability.`
            }],
            temperature: 0.3,
            max_tokens: 1500
          });
          
          response = responseGeneration.choices[0].message.content;
        }
        
        return {
          transcription: transcribedText,
          isQuestion: analysis.isQuestion,
          questionType: analysis.questionType,
          response,
          timestamp: Date.now()
        };
        
      } catch (error) {
        console.error("Error processing audio with OpenAI:", error);
        throw new Error("Failed to process audio with OpenAI API");
      }
    }
    
    throw new Error("Audio processing requires Gemini or OpenAI API");
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    // Enhanced memory cleanup
    this.deps.setHasDebugged(false)
    this.deps.setProblemInfo(null)
    
    // Clear screenshot queues to prevent memory buildup
    this.deps.clearQueues()
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc()
    }
    
    console.log('Memory cleanup completed')

    const mainWindow = this.deps.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
    }
  }

  public cleanup(): void {
    this.cancelOngoingRequests()
    this.openaiClient = null
    this.geminiApiKey = null
    this.anthropicClient = null
    this.grokClient = null
  }
}
