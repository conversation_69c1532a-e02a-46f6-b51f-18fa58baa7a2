import React, { useState, useEffect, useRef } from "react"
import { useQuery } from "@tanstack/react-query"
import ScreenshotQueue from "../components/Queue/ScreenshotQueue"
import QueueCommands from "../components/Queue/QueueCommands"

import { useToast } from "../contexts/toast"
import { Screenshot } from "../types/screenshots"

async function fetchScreenshots(): Promise<Screenshot[]> {
  try {
    const existing = await window.electronAPI.getScreenshots()
    return existing
  } catch (error) {
    console.error("Error loading screenshots:", error)
    throw error
  }
}

interface QueueProps {
  setView: (view: "queue" | "solutions" | "debug") => void
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}

const Queue: React.FC<QueueProps> = ({
  setView,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const { showToast } = useToast()

  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const [tooltipHeight, setTooltipHeight] = useState(0)
  const [questionMode, setQuestionMode] = useState<"dsa" | "system-design" | "auto-detect">("auto-detect")
  const contentRef = useRef<HTMLDivElement>(null)

  const {
    data: screenshots = [],
    isLoading,
    refetch
  } = useQuery<Screenshot[]>({
    queryKey: ["screenshots"],
    queryFn: fetchScreenshots,
    staleTime: Infinity,
    gcTime: Infinity,
    refetchOnWindowFocus: false
  })

  // Load question mode from config
  useEffect(() => {
    const loadQuestionMode = async () => {
      try {
        const config = await window.electronAPI.getConfig();
        setQuestionMode(config.questionDetectionMode || "auto-detect");
      } catch (error) {
        console.error("Failed to load question mode:", error);
      }
    };
    loadQuestionMode();
  }, []);

  // Save question mode when changed
  const handleQuestionModeChange = async (mode: "dsa" | "system-design" | "auto-detect") => {
    setQuestionMode(mode);
    try {
      await window.electronAPI.updateConfig({ questionDetectionMode: mode });
      showToast("Mode Updated", `Switched to ${mode === 'dsa' ? 'DSA' : mode === 'system-design' ? 'System Design' : 'Auto-Detect'} mode`, "success");
    } catch (error) {
      console.error("Failed to save question mode:", error);
    }
  };

  const handleDeleteScreenshot = async (index: number) => {
    const screenshotToDelete = screenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        refetch() // Refetch screenshots instead of managing state directly
      } else {
        console.error("Failed to delete screenshot:", response.error)
        showToast("Error", "Failed to delete the screenshot file", "error")
      }
    } catch (error) {
      console.error("Error deleting screenshot:", error)
    }
  }

  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        if (isTooltipVisible) {
          contentHeight += tooltipHeight
        }
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(() => refetch()),
      window.electronAPI.onResetView(() => refetch()),
      window.electronAPI.onDeleteLastScreenshot(async () => {
        if (screenshots.length > 0) {
          const lastScreenshot = screenshots[screenshots.length - 1];
          await handleDeleteScreenshot(screenshots.length - 1);
          // Toast removed as requested
        } else {
          showToast("No Screenshots", "There are no screenshots to delete", "neutral");
        }
      }),
      window.electronAPI.onSolutionError((error: string) => {
        showToast(
          "Processing Failed",
          "There was an error processing your screenshots.",
          "error"
        )
        setView("queue") // Revert to queue if processing fails
        console.error("Processing error:", error)
      }),
      window.electronAPI.onProcessingNoScreenshots(() => {
        showToast(
          "No Screenshots",
          "There are no screenshots to process.",
          "neutral"
        )
      }),
      // Question mode toggle shortcut handler
      window.electronAPI.onToggleQuestionMode(() => {
        const modes: ("dsa" | "system-design" | "auto-detect")[] = ["dsa", "system-design", "auto-detect"];
        const currentIndex = modes.indexOf(questionMode);
        const nextMode = modes[(currentIndex + 1) % modes.length];
        handleQuestionModeChange(nextMode);
      }),
      // Removed out of credits handler - unlimited credits in this version
    ]

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [isTooltipVisible, tooltipHeight, screenshots])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    setIsTooltipVisible(visible)
    setTooltipHeight(height)
  }

  const handleOpenSettings = () => {
    window.electronAPI.openSettingsPortal();
  };
  
  return (
    <div ref={contentRef} className={`bg-transparent w-1/2`}>
      <div className="px-4 py-3">
        <div className="space-y-3 w-fit">
          {/* Question Mode Toggle */}
          <div className="space-y-2 mb-3">
            <div className="flex gap-1">
              <button
                onClick={() => handleQuestionModeChange("dsa")}
                className={`px-3 py-1 text-xs rounded transition ${
                  questionMode === "dsa"
                    ? "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                    : "bg-white/5 text-white/70 hover:bg-white/10"
                }`}
              >
                🧮 DSA
              </button>
              <button
                onClick={() => handleQuestionModeChange("system-design")}
                className={`px-3 py-1 text-xs rounded transition ${
                  questionMode === "system-design"
                    ? "bg-green-500/20 text-green-400 border border-green-500/30"
                    : "bg-white/5 text-white/70 hover:bg-white/10"
                }`}
              >
                🏗️ System Design
              </button>
              <button
                onClick={() => handleQuestionModeChange("auto-detect")}
                className={`px-3 py-1 text-xs rounded transition ${
                  questionMode === "auto-detect"
                    ? "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                    : "bg-white/5 text-white/70 hover:bg-white/10"
                }`}
              >
                🤖 Auto
              </button>
            </div>
            <div className="text-xs text-white/50">
              Mode: {questionMode === 'dsa' ? 'DSA/Coding' : questionMode === 'system-design' ? 'System Design' : 'Auto-Detect'} | Press Ctrl+M to toggle
            </div>
          </div>

          <ScreenshotQueue
            isLoading={false}
            screenshots={screenshots}
            onDeleteScreenshot={handleDeleteScreenshot}
          />

          <QueueCommands
            onTooltipVisibilityChange={handleTooltipVisibilityChange}
            screenshotCount={screenshots.length}
            credits={credits}
            currentLanguage={currentLanguage}
            setLanguage={setLanguage}
          />
        </div>
      </div>
    </div>
  )
}

export default Queue
