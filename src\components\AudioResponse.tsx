import { useState } from 'react';
import { MarkdownRenderer } from './ui/markdown';

interface AudioAnalysisResult {
  transcription: string;
  isQuestion: boolean;
  questionType?: string;
  response?: string;
  timestamp: number;
}

interface AudioResponseProps {
  result: AudioAnalysisResult;
  currentLanguage: string;
  onClose: () => void;
}

export const AudioResponse = ({ result, currentLanguage, onClose }: AudioResponseProps) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getQuestionTypeColor = (type?: string) => {
    switch (type) {
      case 'coding': return 'text-blue-400';
      case 'system-design': return 'text-purple-400';
      case 'database': return 'text-green-400';
      case 'networking': return 'text-cyan-400';
      case 'security': return 'text-red-400';
      case 'devops': return 'text-orange-400';
      case 'architecture': return 'text-indigo-400';
      case 'behavioral-tech': return 'text-pink-400';
      case 'debugging': return 'text-red-500';
      case 'explanation': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };



  return (
    <div className="bg-black/80 border border-white/10 rounded-lg p-4 space-y-3 max-w-2xl">
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-green-400"></div>
          <span className="text-xs text-white/70">
            Audio Processed - {formatTimestamp(result.timestamp)}
          </span>
        </div>
        <button
          onClick={onClose}
          className="text-white/50 hover:text-white/80 text-sm"
        >
          ✕
        </button>
      </div>

      {/* Transcription */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-white">Transcription</h3>
        <div className="bg-white/5 rounded p-3 text-sm text-gray-200">
          "{result.transcription}"
        </div>
      </div>

      {/* Question Detection */}
      {result.isQuestion && (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-white">Question Detected</span>
            {result.questionType && (
              <span className={`text-xs px-2 py-1 rounded ${getQuestionTypeColor(result.questionType)} bg-white/10`}>
                {result.questionType.replace('-', ' ')}
              </span>
            )}
          </div>
        </div>
      )}

      {/* AI Response */}
      {result.response && (
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <h3 className="text-sm font-medium text-white">AI Response</h3>
            <button
              onClick={() => copyToClipboard(result.response!)}
              className="text-xs text-white/60 hover:text-white/80 bg-white/10 hover:bg-white/20 rounded px-2 py-1"
            >
              {copied ? 'Copied!' : 'Copy'}
            </button>
          </div>
          
          <div className="bg-white/5 rounded p-3 text-sm">
            <MarkdownRenderer 
              content={result.response} 
              className="text-sm"
            />
          </div>
        </div>
      )}

      {/* No Question Detected */}
      {!result.isQuestion && (
        <div className="text-xs text-white/50 bg-white/5 rounded p-2">
          No technical question detected in the audio. The transcription is available above.
        </div>
      )}
    </div>
  );
};