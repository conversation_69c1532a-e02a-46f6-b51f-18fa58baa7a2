// ConfigHelper.ts
import fs from "node:fs"
import path from "node:path"
import { app, safeStorage } from "electron"
import { EventEmitter } from "events"
import { OpenAI } from "openai"

interface Config {
  apiKey: string; // Legacy field for backward compatibility
  apiProvider: "openai" | "gemini" | "anthropic" | "grok";
  // Separate API keys for each provider
  openaiApiKey: string;
  geminiApiKey: string;
  anthropicApiKey: string;
  grokApiKey: string;
  extractionModel: string;
  solutionModel: string;
  debuggingModel: string;
  language: string;
  opacity: number;
  // New question detection settings
  questionDetectionMode: "dsa" | "system-design" | "auto-detect";
  systemDesignFocus: "hld" | "lld" | "both";
}

export class ConfigHelper extends EventEmitter {
  private configPath: string;
  private defaultConfig: Config = {
    apiKey: "", // Legacy field
    apiProvider: "gemini",
    openaiApiKey: "",
    geminiApiKey: "",
    anthropic<PERSON><PERSON><PERSON><PERSON>: "",
    grok<PERSON><PERSON><PERSON><PERSON>: "",
    extractionModel: "gemini-2.0-flash",
    solutionModel: "gemini-2.0-flash",
    debuggingModel: "gemini-2.0-flash",
    language: "python",
    opacity: 1.0,
    questionDetectionMode: "auto-detect",
    systemDesignFocus: "both"
  };

  constructor() {
    super();
    // Use the app's user data directory to store the config
    try {
      this.configPath = path.join(app.getPath('userData'), 'config.json');
      console.log('Config path:', this.configPath);
    } catch (err) {
      console.warn('Could not access user data path, using fallback');
      this.configPath = path.join(process.cwd(), 'config.json');
    }
    
    // Ensure the initial config file exists
    this.ensureConfigExists();
  }

  /**
   * Ensure config file exists
   */
  private ensureConfigExists(): void {
    try {
      if (!fs.existsSync(this.configPath)) {
        this.saveConfig(this.defaultConfig);
      }
    } catch (err: unknown) {
      console.error("Error ensuring config exists:", err instanceof Error ? err.message : String(err));
    }
  }

  /**
   * Validate and sanitize model selection to ensure only allowed models are used
   */
  private sanitizeModelSelection(model: string, provider: "openai" | "gemini" | "anthropic" | "grok"): string {
    if (provider === "openai") {
      // Only allow gpt-4o and gpt-4o-mini for OpenAI
      const allowedModels = ['gpt-4o', 'gpt-4o-mini'];
      if (!allowedModels.includes(model)) {
        console.warn(`Invalid OpenAI model specified: ${model}. Using default model: gpt-4o`);
        return 'gpt-4o';
      }
      return model;
    } else if (provider === "gemini")  {
      // Only allow gemini-1.5-pro and gemini-2.0-flash for Gemini
      const allowedModels = ['gemini-1.5-pro', 'gemini-2.0-flash'];
      if (!allowedModels.includes(model)) {
        console.warn(`Invalid Gemini model specified: ${model}. Using default model: gemini-2.0-flash`);
        return 'gemini-2.0-flash'; // Changed default to flash
      }
      return model;
    }  else if (provider === "anthropic") {
      // Only allow Claude models
      const allowedModels = ['claude-3-7-sonnet-20250219', 'claude-3-5-sonnet-20241022', 'claude-3-opus-20240229'];
      if (!allowedModels.includes(model)) {
        console.warn(`Invalid Anthropic model specified: ${model}. Using default model: claude-3-7-sonnet-20250219`);
        return 'claude-3-7-sonnet-20250219';
      }
      return model;
    } else if (provider === "grok") {
      // Only allow Grok models
      const allowedModels = ['grok-2-1212', 'grok-2-vision-1212', 'grok-3', 'grok-3-mini'];
      if (!allowedModels.includes(model)) {
        console.warn(`Invalid Grok model specified: ${model}. Using default model: grok-3`);
        return 'grok-3';
      }
      return model;
    }
    // Default fallback
    return model;
  }

  public loadConfig(): Config {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        const config = JSON.parse(configData);
        
        // Decrypt API keys if they exist
        if (config.apiKey) config.apiKey = this.decryptString(config.apiKey);
        if (config.openaiApiKey) config.openaiApiKey = this.decryptString(config.openaiApiKey);
        if (config.geminiApiKey) config.geminiApiKey = this.decryptString(config.geminiApiKey);
        if (config.anthropicApiKey) config.anthropicApiKey = this.decryptString(config.anthropicApiKey);
        if (config.grokApiKey) config.grokApiKey = this.decryptString(config.grokApiKey);
        
        if (config.apiProvider !== "openai" && config.apiProvider !== "gemini"  && config.apiProvider !== "anthropic" && config.apiProvider !== "grok") {
          config.apiProvider = "gemini";
        }
        
        if (config.extractionModel) {
          config.extractionModel = this.sanitizeModelSelection(config.extractionModel, config.apiProvider);
        }
        if (config.solutionModel) {
          config.solutionModel = this.sanitizeModelSelection(config.solutionModel, config.apiProvider);
        }
        if (config.debuggingModel) {
          config.debuggingModel = this.sanitizeModelSelection(config.debuggingModel, config.apiProvider);
        }
        
        return {
          ...this.defaultConfig,
          ...config
        };
      }
      
      this.saveConfig(this.defaultConfig);
      return this.defaultConfig;
    } catch (err: unknown) {
      console.error("Error loading config:", err instanceof Error ? err.message : String(err));
      return this.defaultConfig;
    }
  }

  /**
   * Save configuration to disk with encrypted API keys
   */
  public saveConfig(config: Config): void {
    try {
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      
      const configToSave = { ...config };
      
      // Encrypt API keys
      if (config.apiKey) configToSave.apiKey = this.encryptString(config.apiKey);
      if (config.openaiApiKey) configToSave.openaiApiKey = this.encryptString(config.openaiApiKey);
      if (config.geminiApiKey) configToSave.geminiApiKey = this.encryptString(config.geminiApiKey);
      if (config.anthropicApiKey) configToSave.anthropicApiKey = this.encryptString(config.anthropicApiKey);
      if (config.grokApiKey) configToSave.grokApiKey = this.encryptString(config.grokApiKey);
      
      fs.writeFileSync(this.configPath, JSON.stringify(configToSave, null, 2));
    } catch (err: unknown) {
      console.error("Error saving config:", err instanceof Error ? err.message : String(err));
    }
  }

  /**
   * Update specific configuration values
   */
  public updateConfig(updates: Partial<Config>): Config {
    try {
      const currentConfig = this.loadConfig();
      let provider = updates.apiProvider || currentConfig.apiProvider;
      
      // Handle provider-specific API key updates
      if (updates.apiKey && !updates.apiProvider) {
        // Auto-detect provider and store in correct field - FIX: Check Anthropic first
        if (updates.apiKey.trim().startsWith('sk-ant-')) {
          provider = "anthropic";
          updates.anthropicApiKey = updates.apiKey;
          console.log("Auto-detected Anthropic API key format");
        } else if (updates.apiKey.trim().startsWith('xai-')) {
          provider = "grok";
          updates.grokApiKey = updates.apiKey;
          console.log("Auto-detected Grok API key format");
        } else if (updates.apiKey.trim().startsWith('sk-')) {
          provider = "openai";
          updates.openaiApiKey = updates.apiKey;
          console.log("Auto-detected OpenAI API key format");
        } else {
          provider = "gemini";
          updates.geminiApiKey = updates.apiKey;
          console.log("Using Gemini API key format (default)");
        }
        updates.apiProvider = provider;
      } else if (updates.apiKey && updates.apiProvider) {
        // Store API key in provider-specific field
        switch (updates.apiProvider) {
          case "openai":
            updates.openaiApiKey = updates.apiKey;
            break;
          case "gemini":
            updates.geminiApiKey = updates.apiKey;
            break;
          case "anthropic":
            updates.anthropicApiKey = updates.apiKey;
            break;
          case "grok":
            updates.grokApiKey = updates.apiKey;
            break;
        }
      }
      
      // If provider is changing, reset models to the default for that provider
      if (updates.apiProvider && updates.apiProvider !== currentConfig.apiProvider) {
        if (updates.apiProvider === "openai") {
          updates.extractionModel = "gpt-4o";
          updates.solutionModel = "gpt-4o";
          updates.debuggingModel = "gpt-4o";
        } else if (updates.apiProvider === "anthropic") {
          updates.extractionModel = "claude-3-7-sonnet-20250219";
          updates.solutionModel = "claude-3-7-sonnet-20250219";
          updates.debuggingModel = "claude-3-7-sonnet-20250219";
        } else if (updates.apiProvider === "grok") {
          updates.extractionModel = "grok-2-vision-1212";
          updates.solutionModel = "grok-3";
          updates.debuggingModel = "grok-3";
        } else {
          updates.extractionModel = "gemini-2.0-flash";
          updates.solutionModel = "gemini-2.0-flash";
          updates.debuggingModel = "gemini-2.0-flash";
        }
      }
      
      // Sanitize model selections in the updates
      if (updates.extractionModel) {
        updates.extractionModel = this.sanitizeModelSelection(updates.extractionModel, provider);
      }
      if (updates.solutionModel) {
        updates.solutionModel = this.sanitizeModelSelection(updates.solutionModel, provider);
      }
      if (updates.debuggingModel) {
        updates.debuggingModel = this.sanitizeModelSelection(updates.debuggingModel, provider);
      }
      
      const newConfig = { ...currentConfig, ...updates };
      this.saveConfig(newConfig);
      
      // Only emit update event for changes other than opacity
      // This prevents re-initializing the AI client when only opacity changes
      if (updates.apiKey !== undefined || updates.apiProvider !== undefined || 
          updates.extractionModel !== undefined || updates.solutionModel !== undefined || 
          updates.debuggingModel !== undefined || updates.language !== undefined) {
        this.emit('config-updated', newConfig);
      }
      
      return newConfig;
    } catch (error: unknown) {
      console.error('Error updating config:', error instanceof Error ? error.message : String(error));
      return this.defaultConfig;
    }
  }

  /**
   * Check if the API key is configured for current provider
   */
  public hasApiKey(): boolean {
    const config = this.loadConfig();
    const currentKey = this.getCurrentApiKey(config);
    return !!currentKey && currentKey.trim().length > 0;
  }

  /**
   * Get the API key for the current provider
   */
  public getCurrentApiKey(config?: Config): string {
    const cfg = config || this.loadConfig();
    switch (cfg.apiProvider) {
      case "openai":
        return cfg.openaiApiKey || cfg.apiKey; // Fallback to legacy
      case "gemini":
        return cfg.geminiApiKey || cfg.apiKey;
      case "anthropic":
        return cfg.anthropicApiKey || cfg.apiKey;
      case "grok":
        return cfg.grokApiKey || cfg.apiKey;
      default:
        return cfg.apiKey;
    }
  }
  
  /**
   * Sanitize and validate input
   */
  private sanitizeInput(input: string): string {
    return input.trim().replace(/[^a-zA-Z0-9\-_]/g, '');
  }

  /**
   * Validate the API key format
   */
  public isValidApiKeyFormat(apiKey: string, provider?: "openai" | "gemini" | "anthropic" | "grok" ): boolean {
    if (!apiKey || typeof apiKey !== 'string') return false;
    const sanitizedKey = this.sanitizeInput(apiKey);
    // If provider is not specified, attempt to auto-detect
    if (!provider) {
      if (apiKey.trim().startsWith('sk-')) {
        if (apiKey.trim().startsWith('sk-ant-')) {
          provider = "anthropic";
        } else {
          provider = "openai";
        }
      } else if (apiKey.trim().startsWith('xai-')) {
        provider = "grok";
      } else {
        provider = "gemini";
      }
    }
    
    if (provider === "openai") {
      return /^sk-[a-zA-Z0-9]{32,}$/.test(sanitizedKey) && sanitizedKey.length >= 40;
    } else if (provider === "gemini") {
      return sanitizedKey.length >= 20 && sanitizedKey.length <= 100;
    } else if (provider === "anthropic") {
      return /^sk-ant-[a-zA-Z0-9]{32,}$/.test(sanitizedKey) && sanitizedKey.length >= 40;
    } else if (provider === "grok") {
      return /^xai-[a-zA-Z0-9]{32,}$/.test(sanitizedKey) && sanitizedKey.length >= 40;
    }
    
    return false;
  }
  
  /**
   * Get the stored opacity value
   */
  public getOpacity(): number {
    const config = this.loadConfig();
    return config.opacity !== undefined ? config.opacity : 1.0;
  }

  /**
   * Set the window opacity value
   */
  public setOpacity(opacity: number): void {
    // Ensure opacity is between 0.1 and 1.0
    const validOpacity = Math.min(1.0, Math.max(0.1, opacity));
    this.updateConfig({ opacity: validOpacity });
  }  
  
  /**
   * Get the preferred programming language
   */
  public getLanguage(): string {
    const config = this.loadConfig();
    return config.language || "python";
  }

  /**
   * Set the preferred programming language
   */
  public setLanguage(language: string): void {
    this.updateConfig({ language });
  }
  
  /**
   * Test API key with the selected provider
   */
  public async testApiKey(apiKey: string, provider?: "openai" | "gemini" | "anthropic" | "grok"): Promise<{valid: boolean, error?: string}> {
    // Auto-detect provider based on key format if not specified
    if (!provider) {
      if (apiKey.trim().startsWith('sk-')) {
        if (apiKey.trim().startsWith('sk-ant-')) {
          provider = "anthropic";
          console.log("Auto-detected Anthropic API key format for testing");
        } else {
          provider = "openai";
          console.log("Auto-detected OpenAI API key format for testing");
        }
      } else if (apiKey.trim().startsWith('xai-')) {
        provider = "grok";
        console.log("Auto-detected Grok API key format for testing");
      } else {
        provider = "gemini";
        console.log("Using Gemini API key format for testing (default)");
      }
    }
    
    if (provider === "openai") {
      return this.testOpenAIKey(apiKey);
    } else if (provider === "gemini") {
      return this.testGeminiKey(apiKey);
    } else if (provider === "anthropic") {
      return this.testAnthropicKey(apiKey);
    } else if (provider === "grok") {
      return this.testGrokKey(apiKey);
    }
    
    return { valid: false, error: "Unknown API provider" };
  }
  
  /**
   * Test OpenAI API key
   */
  private async testOpenAIKey(apiKey: string): Promise<{valid: boolean, error?: string}> {
    try {
      const openai = new OpenAI({ apiKey });
      // Make a simple API call to test the key
      await openai.models.list();
      return { valid: true };
    } catch (error: unknown) {
      console.error('OpenAI API key test failed:', error);
      
      let errorMessage = 'Unknown error validating OpenAI API key';
      
      if (error && typeof error === 'object' && 'status' in error) {
        const status = (error as any).status;
        if (status === 401) {
          errorMessage = 'Invalid API key. Please check your OpenAI key and try again.';
        } else if (status === 429) {
          errorMessage = 'Rate limit exceeded. Your OpenAI API key has reached its request limit or has insufficient quota.';
        } else if (status === 500) {
          errorMessage = 'OpenAI server error. Please try again later.';
        }
      } else if (error instanceof Error) {
        errorMessage = `Error: ${error.message}`;
      }
      
      return { valid: false, error: errorMessage };
    }
  }
  
  /**
   * Test Gemini API key
   * Note: This is a simplified implementation since we don't have the actual Gemini client
   */
  private async testGeminiKey(apiKey: string): Promise<{valid: boolean, error?: string}> {
    try {
      // For now, we'll just do a basic check to ensure the key exists and has valid format
      // In production, you would connect to the Gemini API and validate the key
      if (apiKey && apiKey.trim().length >= 20) {
        // Here you would actually validate the key with a Gemini API call
        return { valid: true };
      }
      return { valid: false, error: 'Invalid Gemini API key format.' };
    } catch (error: unknown) {
      console.error('Gemini API key test failed:', error);
      let errorMessage = 'Unknown error validating Gemini API key';
      
      if (error instanceof Error) {
        errorMessage = `Error: ${error.message}`;
      }
      
      return { valid: false, error: errorMessage };
    }
  }

  /**
   * Test Anthropic API key
   * Note: This is a simplified implementation since we don't have the actual Anthropic client
   */
  private async testAnthropicKey(apiKey: string): Promise<{valid: boolean, error?: string}> {
    try {
      // For now, we'll just do a basic check to ensure the key exists and has valid format
      // In production, you would connect to the Anthropic API and validate the key
      if (apiKey && /^sk-ant-[a-zA-Z0-9]{32,}$/.test(apiKey.trim())) {
        // Here you would actually validate the key with an Anthropic API call
        return { valid: true };
      }
      return { valid: false, error: 'Invalid Anthropic API key format.' };
    } catch (error: unknown) {
      console.error('Anthropic API key test failed:', error);
      let errorMessage = 'Unknown error validating Anthropic API key';
      
      if (error instanceof Error) {
        errorMessage = `Error: ${error.message}`;
      }
      
      return { valid: false, error: errorMessage };
    }
  }

  /**
   * Test Grok API key
   */
  private async testGrokKey(apiKey: string): Promise<{valid: boolean, error?: string}> {
    try {
      if (apiKey && /^xai-[a-zA-Z0-9]{32,}$/.test(apiKey.trim())) {
        return { valid: true };
      }
      return { valid: false, error: 'Invalid Grok API key format.' };
    } catch (error: unknown) {
      console.error('Grok API key test failed:', error);
      let errorMessage = 'Unknown error validating Grok API key';
      
      if (error instanceof Error) {
        errorMessage = `Error: ${error.message}`;
      }
      
      return { valid: false, error: errorMessage };
    }
  }

  /**
   * Encrypt string using Electron's safeStorage
   */
  private encryptString(text: string): string {
    if (!text) return text;
    try {
      const buffer = safeStorage.encryptString(text);
      return buffer.toString('base64');
    } catch (error) {
      console.error('Encryption failed:', error);
      return text; // Fallback to plain text
    }
  }

  /**
   * Decrypt string using Electron's safeStorage
   */
  private decryptString(encryptedText: string): string {
    if (!encryptedText) return encryptedText;
    
    // Check if text is actually encrypted (base64 format)
    if (!this.isEncrypted(encryptedText)) {
      return encryptedText; // Return as-is if not encrypted
    }
    
    try {
      const buffer = Buffer.from(encryptedText, 'base64');
      return safeStorage.decryptString(buffer);
    } catch (error) {
      return encryptedText; // Fallback to original text
    }
  }

  /**
   * Check if string appears to be encrypted (base64 format)
   */
  private isEncrypted(text: string): boolean {
    if (!text || text.length < 10) return false;
    // Check if it's valid base64 and doesn't look like a plain API key
    const base64Regex = /^[A-Za-z0-9+/]+=*$/;
    return base64Regex.test(text) && !text.startsWith('sk-') && !text.startsWith('xai-');
  }
}

// Export a singleton instance
export const configHelper = new ConfigHelper();
