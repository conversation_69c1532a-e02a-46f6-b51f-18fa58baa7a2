import React, { useState } from "react"
import { Plus, X, Play, Zap } from "lucide-react"

interface TestCase {
  input: any
  expectedOutput: any
  description: string
}

interface TestCaseEditorProps {
  testCases: TestCase[]
  onTestCasesChange: (testCases: TestCase[]) => void
  onRunTests: () => void
  onAutoTest: () => void
  isRunning: boolean
}

export const TestCaseEditor: React.FC<TestCaseEditorProps> = ({
  testCases,
  onTestCasesChange,
  onRunTests,
  onAutoTest,
  isRunning
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const addTestCase = () => {
    const newTestCase = {
      input: "",
      expectedOutput: "",
      description: `Test case ${testCases.length + 1}`
    }
    onTestCasesChange([...testCases, newTestCase])
  }

  const removeTestCase = (index: number) => {
    const updated = testCases.filter((_, i) => i !== index)
    onTestCasesChange(updated)
  }

  const updateTestCase = (index: number, field: keyof TestCase, value: any) => {
    const updated = testCases.map((tc, i) => 
      i === index ? { ...tc, [field]: value } : tc
    )
    onTestCasesChange(updated)
  }

  const parseValue = (value: string) => {
    try {
      return JSON.parse(value)
    } catch {
      return value
    }
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-xs text-white/80 hover:text-white transition"
        >
          Test Cases ({testCases.length}) {isExpanded ? '▼' : '▶'}
        </button>
        <div className="flex gap-2">
          <button
            onClick={onAutoTest}
            disabled={isRunning}
            className="flex items-center gap-1 text-xs text-white bg-purple-500/20 hover:bg-purple-500/30 rounded px-2 py-1 transition disabled:opacity-50"
          >
            <Zap size={12} />
            Auto Test
          </button>
          <button
            onClick={onRunTests}
            disabled={isRunning || testCases.length === 0}
            className="flex items-center gap-1 text-xs text-white bg-green-500/20 hover:bg-green-500/30 rounded px-2 py-1 transition disabled:opacity-50"
          >
            <Play size={12} />
            {isRunning ? "Running..." : "Run Tests"}
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="space-y-2 bg-white/5 rounded-lg p-3">
          {testCases.map((testCase, index) => (
            <div key={index} className="space-y-2 bg-black/20 rounded p-2">
              <div className="flex items-center justify-between">
                <input
                  type="text"
                  value={testCase.description}
                  onChange={(e) => updateTestCase(index, 'description', e.target.value)}
                  className="text-xs bg-transparent border-none outline-none text-white flex-1"
                  placeholder="Test description"
                />
                <button
                  onClick={() => removeTestCase(index)}
                  className="text-red-400 hover:text-red-300 transition"
                >
                  <X size={12} />
                </button>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs text-white/60 block mb-1">Input</label>
                  <textarea
                    value={typeof testCase.input === 'string' ? testCase.input : JSON.stringify(testCase.input)}
                    onChange={(e) => updateTestCase(index, 'input', parseValue(e.target.value))}
                    className="w-full text-xs bg-black/30 border border-white/10 rounded p-2 text-white resize-none"
                    rows={2}
                    placeholder='["hello", 2] or "test"'
                  />
                </div>
                <div>
                  <label className="text-xs text-white/60 block mb-1">Expected Output</label>
                  <textarea
                    value={typeof testCase.expectedOutput === 'string' ? testCase.expectedOutput : JSON.stringify(testCase.expectedOutput)}
                    onChange={(e) => updateTestCase(index, 'expectedOutput', parseValue(e.target.value))}
                    className="w-full text-xs bg-black/30 border border-white/10 rounded p-2 text-white resize-none"
                    rows={2}
                    placeholder='"result" or [1, 2]'
                  />
                </div>
              </div>
            </div>
          ))}
          
          <button
            onClick={addTestCase}
            className="flex items-center gap-1 text-xs text-white/60 hover:text-white transition"
          >
            <Plus size={12} />
            Add Test Case
          </button>
        </div>
      )}
    </div>
  )
}