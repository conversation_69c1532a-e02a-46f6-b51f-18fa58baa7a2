# AI Processing Flow Diagrams

## 🤖 AI Provider Selection & Processing

```mermaid
flowchart TD
    ConfigLoad[Load Configuration] --> ProviderCheck{Which AI Provider?}
    
    ProviderCheck -->|OpenAI| InitOpenAI[Initialize OpenAI Client]
    ProviderCheck -->|Gemini| InitGemini[Initialize Gemini API]
    ProviderCheck -->|Claude| InitClaude[Initialize Anthropic Client]
    ProviderCheck -->|Grok| InitGrok[Initialize Grok Client]
    
    InitOpenAI --> OpenAIReady[OpenAI Ready]
    InitGemini --> GeminiReady[Gemini Ready]
    InitClaude --> ClaudeReady[Claude Ready]
    InitGrok --> GrokReady[Grok Ready]
    
    OpenAIReady --> ProcessingStart[Start Processing]
    GeminiReady --> ProcessingStart
    ClaudeReady --> ProcessingStart
    GrokReady --> ProcessingStart
    
    ProcessingStart --> RateLimit{Check Rate Limit}
    RateLimit -->|OK| SendRequest[Send API Request]
    RateLimit -->|Exceeded| WaitRetry[Wait & Retry]
    
    WaitRetry --> RateLimit
    
    SendRequest --> APIResponse{Response OK?}
    APIResponse -->|Success| ParseResponse[Parse Response]
    APIResponse -->|Error| HandleError[Handle API Error]
    
    HandleError --> RetryLogic{Retry Possible?}
    RetryLogic -->|Yes| WaitRetry
    RetryLogic -->|No| ErrorResponse[Return Error]
    
    ParseResponse --> ValidateResponse{Valid Response?}
    ValidateResponse -->|Yes| ProcessSuccess[Processing Success]
    ValidateResponse -->|No| ErrorResponse
    
    ProcessSuccess --> ReturnResult[Return Processed Result]
    ErrorResponse --> ReturnError[Return Error Message]
```

## 🔍 Question Detection Algorithm

```mermaid
flowchart TD
    Screenshot[Screenshot Input] --> PreProcess[Preprocess Image]
    PreProcess --> ExtractText[Extract Text Content]
    ExtractText --> AnalyzeMarkers[Analyze Visual Markers]
    
    AnalyzeMarkers --> CodingMarkers{Coding Indicators?}
    AnalyzeMarkers --> SystemMarkers{System Design Indicators?}
    
    CodingMarkers -->|Found| CodingScore[Calculate Coding Score]
    SystemMarkers -->|Found| SystemScore[Calculate System Design Score]
    
    CodingScore --> ScoreComparison{Compare Scores}
    SystemScore --> ScoreComparison
    
    ScoreComparison -->|Coding > System| DetectCoding[Detect as Coding]
    ScoreComparison -->|System > Coding| DetectSystem[Detect as System Design]
    ScoreComparison -->|Equal/Low| DefaultCoding[Default to Coding]
    
    DetectCoding --> ConfidenceCoding[Calculate Coding Confidence]
    DetectSystem --> ConfidenceSystem[Calculate System Confidence]
    DefaultCoding --> ConfidenceCoding
    
    ConfidenceCoding --> FinalResult[Final Detection Result]
    ConfidenceSystem --> FinalResult
    
    subgraph "Coding Indicators"
        CI1[Function Signatures]
        CI2[Input/Output Examples]
        CI3[Constraints]
        CI4[Return Statements]
        CI5[Algorithm Keywords]
    end
    
    subgraph "System Design Indicators"
        SI1[Design a system for...]
        SI2[Scale Requirements]
        SI3[Component Diagrams]
        SI4[Architecture Terms]
        SI5[Non-functional Requirements]
    end
    
    CodingMarkers --> CI1
    CodingMarkers --> CI2
    CodingMarkers --> CI3
    CodingMarkers --> CI4
    CodingMarkers --> CI5
    
    SystemMarkers --> SI1
    SystemMarkers --> SI2
    SystemMarkers --> SI3
    SystemMarkers --> SI4
    SystemMarkers --> SI5
```

## 🧠 AI Response Processing Pipeline

```mermaid
sequenceDiagram
    participant Client as Processing Helper
    participant Provider as AI Provider
    participant Parser as Response Parser
    participant Validator as Response Validator
    participant Cache as Memory Cache
    
    Client->>Provider: Send Request with Images/Prompt
    Provider->>Provider: Process Request
    Provider->>Client: Return Raw Response
    
    Client->>Parser: Parse Raw Response
    Parser->>Parser: Extract JSON/Structured Data
    Parser->>Client: Return Parsed Data
    
    Client->>Validator: Validate Parsed Data
    Validator->>Validator: Check Required Fields
    Validator->>Validator: Validate Data Types
    Validator->>Client: Return Validation Result
    
    alt Validation Success
        Client->>Cache: Store Valid Response
        Cache->>Client: Confirm Storage
        Client->>Client: Return Success Result
    else Validation Failed
        Client->>Client: Generate Error Response
        Client->>Client: Return Error Result
    end
```

## 🔄 Retry & Error Handling Flow

```mermaid
flowchart TD
    APICall[Make API Call] --> CheckResponse{Response Status}
    
    CheckResponse -->|200 OK| Success[Process Success]
    CheckResponse -->|401 Unauthorized| InvalidKey[Invalid API Key Error]
    CheckResponse -->|429 Rate Limited| RateLimit[Rate Limit Hit]
    CheckResponse -->|500 Server Error| ServerError[Server Error]
    CheckResponse -->|Network Error| NetworkError[Network Error]
    
    InvalidKey --> FinalError[Return Final Error]
    
    RateLimit --> RetryCheck{Retry Attempts < 3?}
    ServerError --> RetryCheck
    NetworkError --> RetryCheck
    
    RetryCheck -->|Yes| WaitBackoff[Wait with Exponential Backoff]
    RetryCheck -->|No| FinalError
    
    WaitBackoff --> IncrementRetry[Increment Retry Count]
    IncrementRetry --> APICall
    
    Success --> ValidateResponse{Valid Response Format?}
    ValidateResponse -->|Yes| ProcessResponse[Process Response Data]
    ValidateResponse -->|No| ParseError[Parse Error]
    
    ParseError --> RetryCheck
    ProcessResponse --> ReturnSuccess[Return Success Result]
    FinalError --> LogError[Log Error Details]
    LogError --> ReturnError[Return Error Result]
```

## 📊 Confidence Scoring Algorithm

```mermaid
flowchart TD
    ExtractedData[Extracted Problem Data] --> InitScore[Initialize Score = 0]
    InitScore --> CheckFields[Check Required Fields]
    
    CheckFields --> HasProblem{Has problem_statement?}
    HasProblem -->|Yes| AddScore1[Score += 20]
    HasProblem -->|No| AddScore1
    
    AddScore1 --> HasConstraints{Has constraints?}
    HasConstraints -->|Yes| AddScore2[Score += 15]
    HasConstraints -->|No| AddScore2
    
    AddScore2 --> HasExamples{Has input/output examples?}
    HasExamples -->|Yes| AddScore3[Score += 25]
    HasExamples -->|No| AddScore3
    
    AddScore3 --> HasComplexity{Has complexity hints?}
    HasComplexity -->|Yes| AddScore4[Score += 15]
    HasComplexity -->|No| AddScore4
    
    AddScore4 --> HasKeywords{Has algorithm keywords?}
    HasKeywords -->|Yes| AddScore5[Score += 15]
    HasKeywords -->|No| AddScore5
    
    AddScore5 --> HasDifficulty{Has difficulty level?}
    HasDifficulty -->|Yes| AddScore6[Score += 10]
    HasDifficulty -->|No| AddScore6
    
    AddScore6 --> FinalScore[Calculate Final Score]
    FinalScore --> ConfidenceLevel{Score >= 70?}
    
    ConfidenceLevel -->|Yes| HighConfidence[High Confidence]
    ConfidenceLevel -->|No| LowConfidence[Low Confidence]
    
    HighConfidence --> ProceedProcessing[Proceed with Processing]
    LowConfidence --> WarnUser[Warn User about Low Confidence]
    
    WarnUser --> UserDecision{User Confirms?}
    UserDecision -->|Yes| ProceedProcessing
    UserDecision -->|No| RequestRetake[Request Better Screenshot]
```

## 🎯 Solution Generation Pipeline

```mermaid
graph TD
    subgraph "DSA Solution Generation"
        DSAInput[DSA Problem Input] --> DSAPrompt[Create 3-Tier Prompt]
        DSAPrompt --> DSARequest[Send to AI Provider]
        DSARequest --> DSAResponse[Receive AI Response]
        DSAResponse --> DSAParse[Parse 3 Solutions]
        DSAParse --> DSAValidate[Validate Each Solution]
        DSAValidate --> DSAStore[Store Solutions]
    end
    
    subgraph "System Design Generation"
        SystemInput[System Design Input] --> SystemPrompt[Create Comprehensive Prompt]
        SystemPrompt --> SystemRequest[Send to AI Provider]
        SystemRequest --> SystemResponse[Receive AI Response]
        SystemResponse --> SystemParse[Parse HLD/LLD/Code/Diagrams]
        SystemParse --> SystemValidate[Validate Each Section]
        SystemValidate --> SystemStore[Store System Design]
    end
    
    subgraph "Response Processing"
        DSAStore --> FormatDSA[Format DSA Response]
        SystemStore --> FormatSystem[Format System Design Response]
        FormatDSA --> SendToUI[Send to UI]
        FormatSystem --> SendToUI
    end
    
    SendToUI --> DisplaySolutions[Display Solutions to User]
```