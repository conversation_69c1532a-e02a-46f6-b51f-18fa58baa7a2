import { useState, useRef } from 'react';
import { useToast } from '../contexts/toast';

interface AudioAnalysisResult {
  transcription: string;
  isQuestion: boolean;
  questionType?: string;
  response?: string;
  timestamp: number;
}

interface VoiceRecorderProps {
  onResult: (result: AudioAnalysisResult) => void;
  disabled?: boolean;
}

export const VoiceRecorder = ({ onResult, disabled }: VoiceRecorderProps) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioLevel, setAudioLevel] = useState(0);
  const chunks = useRef<Blob[]>([]);
  const audioContext = useRef<AudioContext | null>(null);
  const analyser = useRef<AnalyserNode | null>(null);
  const { showToast } = useToast();

  // Audio level monitoring
  const monitorAudioLevel = (stream: MediaStream) => {
    audioContext.current = new AudioContext();
    analyser.current = audioContext.current.createAnalyser();
    const source = audioContext.current.createMediaStreamSource(stream);
    source.connect(analyser.current);
    
    analyser.current.fftSize = 256;
    const bufferLength = analyser.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const updateLevel = () => {
      if (analyser.current && isRecording) {
        analyser.current.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / bufferLength;
        setAudioLevel(average);
        requestAnimationFrame(updateLevel);
      }
    };
    updateLevel();
  };

  // Enhanced audio processing
  const processAudioBlob = async (blob: Blob): Promise<Blob> => {
    return new Promise((resolve) => {
      const audioContext = new AudioContext();
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
          
          // Apply audio enhancements
          const enhancedBuffer = enhanceAudio(audioBuffer, audioContext);
          
          // Convert back to blob
          const offlineContext = new OfflineAudioContext(
            enhancedBuffer.numberOfChannels,
            enhancedBuffer.length,
            enhancedBuffer.sampleRate
          );
          
          const source = offlineContext.createBufferSource();
          source.buffer = enhancedBuffer;
          source.connect(offlineContext.destination);
          source.start();
          
          const renderedBuffer = await offlineContext.startRendering();
          const wavBlob = audioBufferToWav(renderedBuffer);
          resolve(wavBlob);
        } catch (error) {
          console.warn('Audio enhancement failed, using original:', error);
          resolve(blob);
        }
      };
      reader.readAsArrayBuffer(blob);
    });
  };

  // Audio enhancement function
  const enhanceAudio = (buffer: AudioBuffer, context: AudioContext): AudioBuffer => {
    const enhanced = context.createBuffer(
      buffer.numberOfChannels,
      buffer.length,
      buffer.sampleRate
    );
    
    for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
      const inputData = buffer.getChannelData(channel);
      const outputData = enhanced.getChannelData(channel);
      
      for (let i = 0; i < inputData.length; i++) {
        let sample = inputData[i];
        
        // Noise gate (remove very quiet sounds)
        if (Math.abs(sample) < 0.01) sample = 0;
        
        // Amplify quiet sounds
        if (Math.abs(sample) > 0.01 && Math.abs(sample) < 0.3) {
          sample *= 2.5;
        }
        
        // Soft limiting
        if (sample > 0.95) sample = 0.95;
        if (sample < -0.95) sample = -0.95;
        
        outputData[i] = sample;
      }
    }
    
    return enhanced;
  };

  // Convert AudioBuffer to WAV
  const audioBufferToWav = (buffer: AudioBuffer): Blob => {
    const length = buffer.length * buffer.numberOfChannels * 2;
    const arrayBuffer = new ArrayBuffer(44 + length);
    const view = new DataView(arrayBuffer);
    
    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, buffer.numberOfChannels, true);
    view.setUint32(24, buffer.sampleRate, true);
    view.setUint32(28, buffer.sampleRate * buffer.numberOfChannels * 2, true);
    view.setUint16(32, buffer.numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length, true);
    
    // Convert samples
    let offset = 44;
    for (let i = 0; i < buffer.length; i++) {
      for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' });
  };

  const toggleRecording = async () => {
    if (isRecording) {
      mediaRecorder?.stop();
      setIsRecording(false);
      setAudioLevel(0);
      setIsProcessing(true);
      showToast('Processing', 'Enhancing and analyzing audio...', 'neutral');
    } else {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 44100,
            channelCount: 1
          }
        });
        
        // Start audio level monitoring
        monitorAudioLevel(stream);
        
        const recorder = new MediaRecorder(stream, {
          mimeType: 'audio/webm;codecs=opus',
          audioBitsPerSecond: 128000
        });
        
        recorder.ondataavailable = (e) => {
          if (e.data.size > 0) chunks.current.push(e.data);
        };
        
        recorder.onstop = async () => {
          const originalBlob = new Blob(chunks.current, { type: 'audio/webm' });
          chunks.current = [];
          
          // Stop all tracks and cleanup
          stream.getTracks().forEach(track => track.stop());
          audioContext.current?.close();
          
          try {
            // Enhance audio for better processing
            const enhancedBlob = await processAudioBlob(originalBlob);
            
            const reader = new FileReader();
            reader.onloadend = async () => {
              const base64Data = (reader.result as string).split(',')[1];
              
              try {
                const result = await window.electronAPI.processAudio(
                  base64Data, 
                  enhancedBlob.type || 'audio/wav'
                );
                
                if (result.isQuestion && result.response) {
                  showToast('Question Detected', `${result.questionType} - AI response ready`, 'success');
                } else if (result.isQuestion) {
                  showToast('Question Detected', `${result.questionType} question identified`, 'neutral');
                } else {
                  showToast('Audio Processed', 'Transcription completed', 'neutral');
                }
                
                onResult(result);
              } catch (err) {
                console.error('Audio analysis failed:', err);
                showToast('Error', `Processing failed: ${err.message}`, 'error');
              } finally {
                setIsProcessing(false);
              }
            };
            reader.readAsDataURL(enhancedBlob);
          } catch (error) {
            console.error('Audio enhancement failed:', error);
            setIsProcessing(false);
            showToast('Error', 'Audio processing failed', 'error');
          }
        };
        
        setMediaRecorder(recorder);
        recorder.start(500); // More frequent data collection
        setIsRecording(true);
        showToast('Recording', 'Enhanced voice recording started', 'neutral');
      } catch (err) {
        console.error('Audio recording error:', err);
        showToast('Error', 'Microphone access failed', 'error');
      }
    }
  };

  const getButtonText = () => {
    if (isProcessing) return '⏳ Enhancing...';
    if (isRecording) return `🎤 ${Math.round(audioLevel)}`;
    return 'voice';
  };

  const getButtonStyle = () => {
    if (isProcessing) return 'bg-yellow-500/70 hover:bg-yellow-500/90';
    if (isRecording) {
      const intensity = Math.min(audioLevel / 50, 1);
      return `bg-red-500/${Math.round(70 + intensity * 30)} hover:bg-red-500/90 ${intensity > 0.3 ? 'animate-pulse' : ''}`;
    }
    return 'bg-green-500/70 hover:bg-green-500/90';
  };

  return (
    <button
      onClick={toggleRecording}
      disabled={disabled || isProcessing}
      className={`px-3 py-1 rounded text-xs ${getButtonStyle()} text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed`}
    >
      {getButtonText()}
    </button>
  );
};