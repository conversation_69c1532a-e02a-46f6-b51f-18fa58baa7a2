import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Bold, Italic, Type, Minus, Plus } from "lucide-react";
import DOMPurify from 'dompurify';

interface NotesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function NotesDialog({ open, onOpenChange }: NotesDialogProps) {
  const [content, setContent] = useState("");
  const [fontSize, setFontSize] = useState(14);
  const editorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (open) {
      const saved = localStorage.getItem("codecops-notes");
      const savedFontSize = localStorage.getItem("codecops-notes-fontsize");
      if (saved) setContent(saved);
      if (savedFontSize) setFontSize(parseInt(savedFontSize));
    }
  }, [open]);

  const saveNotes = () => {
    localStorage.setItem("codecops-notes", content);
    localStorage.setItem("codecops-notes-fontsize", fontSize.toString());
  };

  const handleCommand = (command: string) => {
    document.execCommand(command, false);
    if (editorRef.current) {
      setContent(editorRef.current.innerHTML);
    }
  };

  const handleFontSize = (delta: number) => {
    const newSize = Math.max(10, Math.min(24, fontSize + delta));
    setFontSize(newSize);
    saveNotes();
  };

  const handleContentChange = () => {
    if (editorRef.current) {
      const sanitizedContent = DOMPurify.sanitize(editorRef.current.innerHTML, {
        ALLOWED_TAGS: ['b', 'i', 'strong', 'em', 'br', 'p', 'div'],
        ALLOWED_ATTR: []
      });
      setContent(sanitizedContent);
      saveNotes();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg bg-black border border-white/10 text-white">
        <DialogHeader>
          <DialogTitle>Quick Notes</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-3">
          <div className="flex items-center gap-2 p-2 bg-white/5 rounded">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleCommand("bold")}
              className="h-8 w-8 p-0 hover:bg-white/10"
            >
              <Bold size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleCommand("italic")}
              className="h-8 w-8 p-0 hover:bg-white/10"
            >
              <Italic size={14} />
            </Button>
            <div className="flex items-center gap-1 ml-4">
              <Type size={14} />
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleFontSize(-1)}
                className="h-6 w-6 p-0 hover:bg-white/10"
              >
                <Minus size={12} />
              </Button>
              <span className="text-xs w-6 text-center">{fontSize}</span>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleFontSize(1)}
                className="h-6 w-6 p-0 hover:bg-white/10"
              >
                <Plus size={12} />
              </Button>
            </div>
          </div>

          <div
            ref={editorRef}
            contentEditable
            className="min-h-[200px] p-3 bg-black/50 border border-white/10 rounded text-white focus:outline-none focus:ring-1 focus:ring-white/20"
            style={{ fontSize: `${fontSize}px` }}
            onInput={handleContentChange}
            dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(content, {
              ALLOWED_TAGS: ['b', 'i', 'strong', 'em', 'br', 'p', 'div'],
              ALLOWED_ATTR: []
            }) }}
            suppressContentEditableWarning={true}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}