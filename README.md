# CodeCops 🚔

> **AI-Powered Technical Interview Assistant**

An invisible, open-source desktop application that helps you ace technical interviews using AI. Supports voice questions, screenshot analysis, and multi-provider AI integration (OpenAI, Gemini, Claude and Grok X1).

## ✨ Features

### Core Capabilities
- 🎯 **99% Invisible** - Undetectable by Zoom, Discord, and browser screen sharing
- 🎤 **Voice Questions** - Record and get AI responses to technical questions
- 📸 **Smart Screenshots** - Capture coding problems and get instant solutions
- 🤖 **Multi-AI Support** - OpenAI GPT-4, Google Gemini, Anthropic Claude, Grok
- 🔧 **Real-time Debugging** - AI-powered code analysis and fixes
- 🌐 **Multi-Language** - Python, JavaScript, Java, C++, Go, and more
- 🚀 **Intelligent Question Detection** - Auto-detects DSA vs System Design questions
- 🏗️ **Complete System Design Solutions** - HLD, LLD, code examples, and diagrams
- 🧮 **Three Solution Approaches** - Brute Force, Less Optimal, and Most Optimal with different complexities

### Question Detection Modes
- **🧮 DSA Mode**: Focuses on coding problems, algorithms, and data structures
- **🏗️ System Design Mode**: Comprehensive architecture solutions with HLD/LLD
- **🤖 Auto-Detect**: Intelligently identifies question type with confidence scoring

### Technical Question Types
- **Coding**: Algorithms, data structures, programming challenges
- **System Design**: Architecture, scalability, distributed systems with step-by-step breakdowns
- **Database**: SQL, NoSQL, optimization, design patterns
- **DevOps**: CI/CD, containerization, cloud platforms
- **Security**: Authentication, encryption, best practices
- **Networking**: Protocols, performance, troubleshooting

### Solution Approaches

#### For DSA Problems:
- **🔴 Brute Force**: O(n²) complexity - Simple, straightforward solutions with nested loops
- **🟡 Less Optimal**: O(n log n) complexity - Moderately optimized using sorting or divide & conquer
- **🟢 Most Optimal**: O(n) complexity - Single-pass solutions with optimal data structures

#### For System Design Problems:
- **🏗️ High Level Design (HLD)**: Architecture overview, components, data flow, scalability
- **🔧 Low Level Design (LLD)**: Database schemas, API specs, core classes with full implementation
- **💻 Code Examples**: Complete working code in Python/Java with detailed explanations
- **📊 Diagrams**: ASCII architecture diagrams, ER diagrams, sequence flows

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- AI API Key (OpenAI, Gemini, or Anthropic)

### Installation

```bash
# Clone repository
git clone https://github.com/Rehan018/codeCops.git
cd codeCops

# Install dependencies
npm install

# Clean previous builds (recommended)
npm run clean

# Run application
npm run dev
```

### First Launch Setup
1. Application starts **invisible** - press `Ctrl+B` (or `Cmd+B`) to show
2. Configure your AI API key in Settings
3. Start taking screenshots or recording voice questions

## 🎮 Global Shortcuts

| Action | Windows/Linux | macOS |
|--------|---------------|-------|
| Toggle Visibility | `Ctrl + B` | `Cmd + B` |
| Take Screenshot | `Ctrl + H` | `Cmd + H` |
| Record Voice | Click Voice Button | Click Voice Button |
| Process Screenshots | `Ctrl + Enter` | `Cmd + Enter` |
| **Toggle Question Mode** | `Ctrl + M` | `Cmd + M` |
| Delete Last Screenshot | `Ctrl + L` | `Cmd + L` |
| Reset/New Problem | `Ctrl + R` | `Cmd + R` |
| Move Window | `Ctrl + Arrow Keys` | `Cmd + Arrow Keys` |
| Adjust Opacity | `Ctrl + [ / ]` | `Cmd + [ / ]` |
| Zoom | `Ctrl + -/0/=` | `Cmd + -/0/=` |
| Brute Force Solution | `Ctrl + Shift + B` | `Cmd + Shift + B` |
| Less Optimal Solution | `Ctrl + Shift + L` | `Cmd + Shift + L` |
| Most Optimal Solution | `Ctrl + Shift + M` | `Cmd + Shift + M` |
| Quit | `Ctrl + Q` | `Cmd + Q` |

## 🔄 Interactive Solutions

### Question Mode Selection
Choose your question type for optimal AI processing:
- **🧮 DSA Button** - For coding/algorithm problems
- **🏗️ System Design Button** - For architecture problems  
- **🤖 Auto Button** - Let AI detect the question type
- **Ctrl+M** - Keyboard shortcut to cycle through modes

### DSA Solutions
After processing coding screenshots, you'll see three solution buttons:
- **🔴 Brute Force** - Click to view O(n²) solution with detailed explanation
- **🟡 Less Optimal** - Click to view O(n log n) solution with moderate optimization  
- **🟢 Most Optimal** - Click to view O(n) solution with best time complexity

### System Design Solutions
For architecture problems, you'll get comprehensive analysis:
- **Step 1: HLD** - High-level architecture with component breakdown
- **Step 2: LLD** - Database schemas, API specifications, core classes
- **Step 3: Diagrams** - ASCII architecture diagrams and data flow
- **Step 4: Implementation** - Complete working code examples
- **Step 5: Scalability** - Performance optimization and monitoring strategies

Each solution includes:
- Complete code implementation
- Step-by-step approach explanation
- Algorithm details and dry run (for DSA)
- Architecture diagrams and system flow (for System Design)
- Individual complexity analysis

## 🔧 Configuration

### API Keys Setup

#### OpenAI
1. Get API key from [OpenAI Platform](https://platform.openai.com/api-keys)
2. Format: `sk-...`
3. Models: GPT-4o, GPT-4o-mini

#### Google Gemini
1. Get API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Models: Gemini 1.5 Pro, Gemini 2.0 Flash
3. **Supports audio processing**

#### Anthropic Claude
1. Get API key from [Anthropic Console](https://console.anthropic.com/settings/keys)
2. Format: `sk-ant-...`
3. Models: Claude 3.5/3.7 Sonnet, Claude 3 Opus

#### Grok (xAI)
1. Get API key from [xAI Console](https://console.x.ai/settings/keys)
2. Format: `xai-...`
3. Models: Grok 2, Grok 3, Grok 2 Vision

### Model Selection
Configure different models for:
- **Problem Extraction**: Analyze screenshots and detect question types
- **Solution Generation**: Create DSA solutions or system design architectures
- **Debugging**: Analyze errors and improvements

### Question Detection
Choose how the AI should interpret your screenshots:
- **DSA Mode**: Optimized for coding problems, algorithms, data structures
- **System Design Mode**: Focused on architecture, scalability, system components
- **Auto-Detect Mode**: AI automatically determines question type with confidence scoring

## 📦 Building & Deployment

### Development
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run production build
npm run run-prod
```

### Distribution Packages

#### Windows
```bash
# Build Windows installer
npm run package-win

# Output: release/CodeCops-Windows-{version}.exe
```

**Detailed Windows Build Process:**
```bash
# 1. Clean previous builds
npm run clean

# 2. Install dependencies
npm install

# 3. Build the application
npm run build

# 4. Create Windows executable
npm run package-win

# 5. Find your .exe file in:
# release/CodeCops-Windows-{version}.exe
```

**Windows Build Requirements:**
- Node.js 16+
- Python 3.x (for native modules)
- Visual Studio Build Tools or Visual Studio Community
- Windows 10/11

**If build fails, install Windows build tools:**
```bash
# Install windows-build-tools globally
npm install -g windows-build-tools

# Or install Visual Studio Build Tools manually
# Download from: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
```

#### macOS
```bash
# Build macOS DMG
npm run package-mac

# Output: release/CodeCops-{arch}.dmg
```

#### Linux
```bash
# Build Linux AppImage
npm run package

# Output: release/CodeCops-Linux-{version}.AppImage
```

### Manual Installation

#### Windows
1. Download `.exe` installer from releases
2. **Security Warning**: Windows Defender may flag as unknown app
   - Click "More info" → "Run anyway"
   - Or add to Windows Defender exclusions
3. Run installer as Administrator (recommended)
4. Grant microphone permissions if using voice features
5. App will be installed to `C:\Users\<USER>\AppData\Local\Programs\CodeCops`

**Creating Your Own .exe:**
```bash
# Prerequisites
- Windows 10/11
- Node.js 16+
- Git
- Visual Studio Build Tools

# Step-by-step build
git clone https://github.com/Rehan018/codeCops.git
cd codecops
npm install
npm run clean
npm run build
npm run package-win

# Your .exe will be in: release/CodeCops-Windows-{version}.exe
```

**Troubleshooting Windows Build:**
- **Error: MSBuild not found** → Install Visual Studio Build Tools
- **Error: Python not found** → Install Python 3.x and add to PATH
- **Error: node-gyp fails** → Run `npm install -g windows-build-tools`
- **Antivirus blocking** → Add project folder to exclusions

#### macOS
1. Download `.dmg` file
2. Drag CodeCops to Applications
3. First launch: Right-click → Open (bypass Gatekeeper)
4. Grant Screen Recording permission in System Preferences

#### Linux
1. Download `.AppImage` file
2. Make executable: `chmod +x CodeCops-Linux-*.AppImage`
3. Run: `./CodeCops-Linux-*.AppImage`

## 🔒 Privacy & Security

### Data Handling
- **API Keys**: Stored locally, never transmitted except to chosen AI provider
- **Screenshots**: Processed locally, sent only to AI APIs
- **Voice Data**: Converted to text locally, then processed by AI
- **No Telemetry**: Zero usage tracking or data collection

### Invisibility Compatibility
✅ **Invisible to:**
- Zoom (versions < 6.1.6)
- Discord screen sharing
- Browser-based screen recording
- macOS screenshot (Cmd+Shift+3/4)

❌ **Visible to:**
- Zoom 6.1.6+ 
- macOS screen recording (Cmd+Shift+5)

## 🛠️ Development

### Tech Stack
- **Frontend**: React + TypeScript + Vite
- **Backend**: Electron + Node.js
- **UI**: Tailwind CSS + Radix UI
- **AI**: OpenAI, Gemini, Anthropic APIs
- **Solution Engine**: Multi-complexity algorithm generation

### Project Structure
```
├── electron/           # Main process
│   ├── main.ts        # App initialization
│   ├── ProcessingHelper.ts  # AI integration & solution parsing
│   └── ScreenshotHelper.ts  # Screenshot capture
├── src/               # Renderer process
│   ├── components/    # React components
│   │   └── Solutions/ # Solution selector & display
│   ├── _pages/        # Main app views
│   └── types/         # TypeScript definitions
└── dist-electron/     # Built electron files
```

### Adding New AI Providers
1. Extend `ProcessingHelper.ts`
2. Add provider config in `ConfigHelper.ts`
3. Update UI in `SettingsDialog.tsx`

### Custom Features
- Modify keyboard shortcuts in `shortcuts.ts`
- Add new question types in audio processing
- Extend language support in components
- Customize solution complexity requirements in AI prompts

### Solution System
- **Three-tier approach**: Each problem generates brute force, less optimal, and most optimal solutions
- **Interactive UI**: Click buttons to switch between solution approaches instantly
- **Individual analysis**: Each solution has unique code, explanation, and complexity
- **Complexity validation**: AI enforces different time complexities (O(n²), O(n log n), O(n))

## 🔨 Windows .exe Creation Guide

### Prerequisites
```bash
# Required software
- Node.js 16+ (from nodejs.org)
- Git (from git-scm.com)
- Visual Studio Build Tools
- Python 3.x
```

### Install Build Tools
```bash
# Option 1: Using winget
winget install Microsoft.VisualStudio.2022.BuildTools

# Option 2: Using npm
npm install -g windows-build-tools

# Option 3: Manual download
# https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
```

### Step-by-Step Build Process
```bash
# 1. Clone repository
git clone https://github.com/Rehan018/codeCops.git
cd codeCops

# 2. Install dependencies
npm install

# 3. Clean previous builds
npm run clean

# 4. Build application
npm run build

# 5. Create Windows executable
npm run package-win

# 6. Your .exe will be created at:
# release/CodeCops-Windows-{version}.exe
```

### Build Troubleshooting

| Error | Solution |
|-------|----------|
| `MSBuild not found` | Install Visual Studio Build Tools |
| `Python not found` | Install Python 3.x and add to PATH |
| `node-gyp rebuild failed` | Run `npm install -g windows-build-tools` |
| `Permission denied` | Run Command Prompt as Administrator |
| `Antivirus blocking` | Add project folder to antivirus exclusions |
| `Out of memory` | Close other applications and retry |
| `ENOENT: no such file` | Run `npm run clean` then rebuild |

### Distribution Notes
- Built .exe size: ~150-200MB (includes Chromium)
- Windows Defender may show security warning
- Users need to click "More info" → "Run anyway"
- For production distribution, consider code signing certificate

## 🚨 Troubleshooting

### Common Issues

**App won't start**
```bash
npm run clean
npm install
npm run dev
```

**Window not visible**
- Press `Ctrl+B` / `Cmd+B` multiple times
- Check opacity with `Ctrl+]` / `Cmd+]`

**Audio processing fails**
- Verify API key in Settings
- Check browser microphone permissions
- Try different AI provider (Gemini recommended for audio)

**Screenshots not working**
- Grant screen recording permissions
- Check if window is properly hidden
- Verify global shortcuts aren't conflicting

### Permissions

#### macOS
```bash
# Grant screen recording permission
System Preferences → Security & Privacy → Privacy → Screen Recording
# Add CodeCops and restart app
```

#### Windows
- No additional permissions needed
- Windows Defender may flag as unknown app

#### Linux
```bash
# May need X11 access
xhost +local:
```

## 📊 Performance

### Resource Usage
- **RAM**: ~100-200MB
- **CPU**: Minimal when idle
- **Network**: Only during AI API calls
- **Storage**: ~50MB installed

### API Costs (Approximate)
- **Screenshot Analysis**: $0.01-0.05 per image
- **Voice Processing**: $0.001-0.01 per minute
- **Solution Generation**: $0.01-0.03 per response

## 🤝 Contributing

### Development Setup
```bash
git clone https://github.com/Rehan018/codeCops.git
cd codeCops
npm install
npm run dev
```

### Contribution Areas
- 🐛 Bug fixes and improvements
- 🎨 UI/UX enhancements
- 🤖 New AI provider integrations
- 📚 Documentation updates
- 🌐 Internationalization

## 📄 License

**GNU Affero General Public License v3.0 (AGPL-3.0)**

- ✅ Use, modify, distribute freely
- ✅ Commercial use allowed
- ⚠️ Must share modifications under same license
- ⚠️ Network use requires source disclosure



## 🔗 Links

- **Repository**: [GitHub](https://github.com/Rehan018/codeCops)
- **Issues**: [Bug Reports](https://github.com/Rehan018/codeCops/issues)
- **Releases**: [Downloads](https://github.com/Rehan018/codeCops/releases)

---

## 🆕 Latest Updates

### v1.0.19 - Enhanced Question Detection
- **🎯 Smart Question Detection**: Auto-detects DSA vs System Design questions
- **🏗️ Complete System Design Solutions**: Step-by-step HLD/LLD with code examples
- **⌨️ Quick Mode Toggle**: Ctrl+M to switch between question types
- **📊 Confidence Scoring**: Shows detection accuracy percentage
- **🔧 Enhanced AI Prompts**: Improved accuracy with specific visual markers
- **🚀 4 AI Providers**: OpenAI, Gemini, Claude, and Grok support

---

**⚡ Ready to ace your technical interviews? Download CodeCops and let AI be your coding companion!**
