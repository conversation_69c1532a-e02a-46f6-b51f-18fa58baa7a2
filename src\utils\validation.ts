import DOMPurify from 'dompurify';

export class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export const validators = {
  // Sanitize HTML content
  sanitizeHtml: (input: string): string => {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['b', 'i', 'strong', 'em', 'br', 'p', 'div', 'code', 'pre'],
      ALLOWED_ATTR: []
    });
  },

  // Validate API key format
  validateApiKey: (key: string, provider: string): boolean => {
    if (!key || typeof key !== 'string') return false;
    
    const sanitized = key.trim().replace(/[^a-zA-Z0-9\-_]/g, '');
    
    switch (provider) {
      case 'openai':
        return /^sk-[a-zA-Z0-9]{32,}$/.test(sanitized) && sanitized.length >= 40;
      case 'anthropic':
        return /^sk-ant-[a-zA-Z0-9]{32,}$/.test(sanitized) && sanitized.length >= 40;
      case 'grok':
        return /^xai-[a-zA-Z0-9]{32,}$/.test(sanitized) && sanitized.length >= 40;
      case 'gemini':
        return sanitized.length >= 20 && sanitized.length <= 100;
      default:
        return false;
    }
  },

  // Validate file paths
  validateFilePath: (path: string): boolean => {
    if (!path || typeof path !== 'string') return false;
    // Basic path validation - no directory traversal
    return !path.includes('..') && !path.includes('~') && path.length < 500;
  },

  // Validate programming language
  validateLanguage: (lang: string): boolean => {
    const allowedLanguages = ['python', 'javascript', 'java', 'cpp', 'c', 'go', 'rust', 'typescript'];
    return allowedLanguages.includes(lang.toLowerCase());
  },

  // Validate model names
  validateModel: (model: string, provider: string): boolean => {
    const modelMap = {
      openai: ['gpt-4o', 'gpt-4o-mini'],
      gemini: ['gemini-1.5-pro', 'gemini-2.0-flash'],
      anthropic: ['claude-3-7-sonnet-20250219', 'claude-3-5-sonnet-20241022', 'claude-3-opus-20240229'],
      grok: ['grok-2-1212', 'grok-2-vision-1212', 'grok-3', 'grok-3-mini']
    };
    
    return modelMap[provider as keyof typeof modelMap]?.includes(model) || false;
  },

  // Sanitize user input
  sanitizeInput: (input: string, maxLength = 1000): string => {
    if (!input || typeof input !== 'string') return '';
    return input.trim().substring(0, maxLength);
  }
};

export const validate = {
  required: (value: any, fieldName: string) => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      throw new ValidationError(`${fieldName} is required`, fieldName);
    }
  },

  string: (value: any, fieldName: string, maxLength = 1000) => {
    if (typeof value !== 'string') {
      throw new ValidationError(`${fieldName} must be a string`, fieldName);
    }
    if (value.length > maxLength) {
      throw new ValidationError(`${fieldName} is too long (max ${maxLength} characters)`, fieldName);
    }
  },

  apiKey: (key: string, provider: string) => {
    if (!validators.validateApiKey(key, provider)) {
      throw new ValidationError(`Invalid ${provider} API key format`);
    }
  }
};