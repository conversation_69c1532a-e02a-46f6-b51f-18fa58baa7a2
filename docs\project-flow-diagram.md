# CodeCops - Complete Project Flow Diagram

## 🏗️ Overall System Architecture

```mermaid
graph TB
    subgraph "User Interface Layer"
        UI[React Frontend]
        Queue[Queue Page]
        Solutions[Solutions Page]
        Settings[Settings Dialog]
    end
    
    subgraph "Main Process (Electron)"
        Main[main.ts]
        IPC[IPC Handlers]
        Config[ConfigHelper]
        Processing[ProcessingHelper]
        Screenshot[ScreenshotHelper]
        Shortcuts[ShortcutsHelper]
    end
    
    subgraph "AI Providers"
        OpenAI[OpenAI GPT-4]
        Gemini[Google Gemini]
        Claude[Anthropic Claude]
        Grok[xAI Grok]
    end
    
    subgraph "Storage"
        LocalConfig[Local Config]
        Screenshots[Screenshot Files]
        Cache[Memory Cache]
    end
    
    UI --> IPC
    IPC --> Processing
    IPC --> Screenshot
    IPC --> Config
    Processing --> OpenAI
    Processing --> Gemini
    Processing --> Claude
    Processing --> Grok
    Config --> LocalConfig
    Screenshot --> Screenshots
    Main --> Shortcuts
```

## 🔄 Complete Application Flow

```mermaid
flowchart TD
    Start([App Launch]) --> Init[Initialize Electron App]
    Init --> LoadConfig[Load Configuration]
    LoadConfig --> CheckAPI{API Key Exists?}
    
    CheckAPI -->|No| Welcome[Show Welcome Screen]
    Welcome --> OpenSettings[Open Settings Dialog]
    OpenSettings --> SaveAPI[Save API Key & Config]
    SaveAPI --> Restart[Restart App]
    
    CheckAPI -->|Yes| ShowQueue[Show Queue Interface]
    Restart --> ShowQueue
    
    ShowQueue --> ModeSelect{Select Question Mode}
    ModeSelect --> DSAMode[🧮 DSA Mode]
    ModeSelect --> SystemMode[🏗️ System Design Mode]
    ModeSelect --> AutoMode[🤖 Auto-Detect Mode]
    
    DSAMode --> WaitScreenshot[Wait for Screenshot]
    SystemMode --> WaitScreenshot
    AutoMode --> WaitScreenshot
    
    WaitScreenshot --> TakeScreenshot{User Takes Screenshot?}
    TakeScreenshot -->|Ctrl+H| CaptureScreen[Capture Screenshot]
    TakeScreenshot -->|Voice| RecordAudio[Record Audio Question]
    
    CaptureScreen --> SaveScreenshot[Save to Queue]
    SaveScreenshot --> ShowPreview[Show Screenshot Preview]
    ShowPreview --> WaitProcess{Process Screenshots?}
    
    WaitProcess -->|Ctrl+Enter| StartProcessing[Start AI Processing]
    WaitProcess -->|More Screenshots| WaitScreenshot
    
    StartProcessing --> DetectQuestion[Detect Question Type]
    DetectQuestion --> ExtractProblem[Extract Problem Details]
    ExtractProblem --> CalculateConfidence[Calculate Detection Confidence]
    CalculateConfidence --> ShowConfidence[Show Confidence to User]
    
    ShowConfidence --> RouteByType{Question Type?}
    RouteByType -->|DSA/Coding| GenerateDSA[Generate 3-Tier DSA Solutions]
    RouteByType -->|System Design| GenerateSystem[Generate System Design Solution]
    
    GenerateDSA --> DSASolutions[Show Brute Force, Less Optimal, Most Optimal]
    GenerateSystem --> SystemSolution[Show HLD, LLD, Code Examples, Diagrams]
    
    DSASolutions --> SolutionsPage[Navigate to Solutions Page]
    SystemSolution --> SolutionsPage
    
    SolutionsPage --> InteractSolutions{User Interaction}
    InteractSolutions -->|Switch Solution| ChangeSolution[Change Solution Type]
    InteractSolutions -->|Take Debug Screenshot| DebugFlow[Debug Flow]
    InteractSolutions -->|Reset| ClearQueue[Clear Queue & Reset]
    
    ChangeSolution --> SolutionsPage
    ClearQueue --> ShowQueue
    
    DebugFlow --> TakeDebugScreenshot[Take Additional Screenshots]
    TakeDebugScreenshot --> ProcessDebug[Process Debug Request]
    ProcessDebug --> ShowDebugResults[Show Debug Analysis]
    ShowDebugResults --> SolutionsPage
    
    RecordAudio --> ProcessAudio[Process Audio with Gemini]
    ProcessAudio --> AudioResponse[Show Audio Response]
    AudioResponse --> WaitScreenshot
```

## 🎯 Question Detection Flow

```mermaid
flowchart TD
    Screenshot[Screenshot Captured] --> ModeCheck{Current Mode?}
    
    ModeCheck -->|DSA Mode| DSAPrompt[Use DSA-Specific Prompt]
    ModeCheck -->|System Design Mode| SystemPrompt[Use System Design Prompt]
    ModeCheck -->|Auto-Detect| AutoPrompt[Use Auto-Detection Prompt]
    
    DSAPrompt --> DSAExtract[Extract: problem_statement, constraints, examples]
    SystemPrompt --> SystemExtract[Extract: requirements, scale, components]
    AutoPrompt --> AutoDetect[Detect Question Type First]
    
    AutoDetect --> TypeDetected{Type Detected?}
    TypeDetected -->|Coding| DSAExtract
    TypeDetected -->|System Design| SystemExtract
    TypeDetected -->|Unclear| DefaultCoding[Default to Coding]
    
    DSAExtract --> DSAConfidence[Calculate DSA Confidence Score]
    SystemExtract --> SystemConfidence[Calculate System Design Confidence]
    DefaultCoding --> DSAConfidence
    
    DSAConfidence --> ConfidenceCheck{Confidence > 70%?}
    SystemConfidence --> ConfidenceCheck
    
    ConfidenceCheck -->|Yes| ProceedProcessing[Proceed with Solution Generation]
    ConfidenceCheck -->|No| ShowLowConfidence[Show Low Confidence Warning]
    
    ShowLowConfidence --> UserChoice{User Confirms?}
    UserChoice -->|Yes| ProceedProcessing
    UserChoice -->|No| RetakeScreenshot[Suggest Retaking Screenshot]
    
    ProceedProcessing --> GenerateSolution[Generate Appropriate Solution]
    RetakeScreenshot --> Screenshot
```

## 🧮 DSA Solution Generation Flow

```mermaid
flowchart TD
    DSAProblem[DSA Problem Extracted] --> CreatePrompt[Create 3-Tier Solution Prompt]
    CreatePrompt --> SendToAI[Send to Selected AI Provider]
    
    SendToAI --> AIResponse[Receive AI Response]
    AIResponse --> ParseResponse[Parse 3 Solutions]
    
    ParseResponse --> BruteForce[Extract Brute Force Solution]
    ParseResponse --> LessOptimal[Extract Less Optimal Solution]
    ParseResponse --> MostOptimal[Extract Most Optimal Solution]
    
    BruteForce --> ParseBrute[Parse: Code, Thoughts, Complexity]
    LessOptimal --> ParseLess[Parse: Code, Thoughts, Complexity]
    MostOptimal --> ParseMost[Parse: Code, Thoughts, Complexity]
    
    ParseBrute --> ValidateBrute{Valid O n squared Solution?}
    ParseLess --> ValidateLess{Valid O n log n Solution?}
    ParseMost --> ValidateMost{Valid O n Solution?}
    
    ValidateBrute -->|Yes| StoreBrute[Store Brute Force Solution]
    ValidateLess -->|Yes| StoreLess[Store Less Optimal Solution]
    ValidateMost -->|Yes| StoreMost[Store Most Optimal Solution]
    
    ValidateBrute -->|No| DefaultBrute[Use Default Brute Force]
    ValidateLess -->|No| DefaultLess[Use Default Less Optimal]
    ValidateMost -->|No| DefaultMost[Use Default Most Optimal]
    
    StoreBrute --> CombineSolutions[Combine All Solutions]
    StoreLess --> CombineSolutions
    StoreMost --> CombineSolutions
    DefaultBrute --> CombineSolutions
    DefaultLess --> CombineSolutions
    DefaultMost --> CombineSolutions
    
    CombineSolutions --> DisplaySolutions[Display Solution Selector UI]
    DisplaySolutions --> UserSelect[User Selects Solution Type]
    UserSelect --> ShowSelectedSolution[Show Selected Solution Details]
```

## 🏗️ System Design Solution Flow

```mermaid
flowchart TD
    SystemProblem[System Design Problem Extracted] --> CreateSystemPrompt[Create Comprehensive System Design Prompt]
    CreateSystemPrompt --> SendToAI[Send to AI Provider]
    
    SendToAI --> AIResponse[Receive Detailed Response]
    AIResponse --> ParseSections[Parse Response Sections]
    
    ParseSections --> ExtractHLD[Extract High Level Design]
    ParseSections --> ExtractLLD[Extract Low Level Design]
    ParseSections --> ExtractCode[Extract Code Examples]
    ParseSections --> ExtractDiagrams[Extract Diagrams]
    ParseSections --> ExtractPerformance[Extract Performance Analysis]
    
    ExtractHLD --> ParseHLD[Parse: Architecture, Components, Data Flow]
    ExtractLLD --> ParseLLD[Parse: Database Schema, APIs, Classes]
    ExtractCode --> ParseCode[Parse: Python/Java Code Examples]
    ExtractDiagrams --> ParseDiagrams[Parse: ASCII Diagrams, ER Diagrams]
    ExtractPerformance --> ParsePerf[Parse: Bottlenecks, Optimizations]
    
    ParseHLD --> ValidateHLD{Valid Architecture?}
    ParseLLD --> ValidateLLD{Valid Implementation?}
    ParseCode --> ValidateCode{Valid Code Examples?}
    ParseDiagrams --> ValidateDiagrams{Valid Diagrams?}
    ParsePerf --> ValidatePerf{Valid Analysis?}
    
    ValidateHLD -->|Yes| StoreHLD[Store HLD Content]
    ValidateLLD -->|Yes| StoreLLD[Store LLD Content]
    ValidateCode -->|Yes| StoreCode[Store Code Examples]
    ValidateDiagrams -->|Yes| StoreDiagrams[Store Diagrams]
    ValidatePerf -->|Yes| StorePerf[Store Performance Analysis]
    
    ValidateHLD -->|No| DefaultHLD[Use Default HLD Template]
    ValidateLLD -->|No| DefaultLLD[Use Default LLD Template]
    ValidateCode -->|No| DefaultCode[Use Default Code Template]
    ValidateDiagrams -->|No| DefaultDiagrams[Use Default Diagrams]
    ValidatePerf -->|No| DefaultPerf[Use Default Performance]
    
    StoreHLD --> CombineSystemSolution[Combine All System Design Components]
    StoreLLD --> CombineSystemSolution
    StoreCode --> CombineSystemSolution
    StoreDiagrams --> CombineSystemSolution
    StorePerf --> CombineSystemSolution
    DefaultHLD --> CombineSystemSolution
    DefaultLLD --> CombineSystemSolution
    DefaultCode --> CombineSystemSolution
    DefaultDiagrams --> CombineSystemSolution
    DefaultPerf --> CombineSystemSolution
    
    CombineSystemSolution --> DisplaySystemSolution[Display System Design Solution]
    DisplaySystemSolution --> ShowTabs[Show HLD/LLD/Code/Diagrams Tabs]
    ShowTabs --> UserNavigate[User Navigates Between Sections]
```

## 🔧 Technical Architecture Flow

```mermaid
graph TB
    subgraph "Frontend (React)"
        A[App.tsx] --> B[SubscribedApp.tsx]
        B --> C[Queue.tsx]
        B --> D[Solutions.tsx]
        C --> E[QueueCommands.tsx]
        C --> F[ScreenshotQueue.tsx]
        D --> G[SolutionSelector.tsx]
        D --> H[SolutionCommands.tsx]
    end
    
    subgraph "IPC Communication"
        I[preload.ts] --> J[electronAPI]
        J --> K[IPC Events]
    end
    
    subgraph "Main Process"
        L[main.ts] --> M[ProcessingHelper.ts]
        L --> N[ScreenshotHelper.ts]
        L --> O[ConfigHelper.ts]
        L --> P[ShortcutsHelper.ts]
        M --> Q[AI Provider Factory]
    end
    
    subgraph "AI Processing"
        Q --> R[OpenAI Provider]
        Q --> S[Gemini Provider]
        Q --> T[Anthropic Provider]
        Q --> U[Grok Provider]
    end
    
    subgraph "Data Storage"
        V[Config JSON]
        W[Screenshot Files]
        X[Memory Cache]
    end
    
    E --> I
    F --> I
    G --> I
    H --> I
    K --> M
    K --> N
    K --> O
    M --> R
    M --> S
    M --> T
    M --> U
    O --> V
    N --> W
    M --> X
```