import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { tempFileManager } from './TempFileManager';

export class SecureCodeRunner {
  private readonly timeout = 5000; // 5 seconds
  private readonly memoryLimit = 128 * 1024 * 1024; // 128MB
  private tempDir = path.join(process.cwd(), 'temp-validation');

  constructor() {
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
    tempFileManager.registerTempDir(this.tempDir);
  }

  async runPython(code: string, input: any): Promise<{ output: any; error?: string; executionTime: number; memoryUsage: number }> {
    const startTime = Date.now();
    const tempFile = path.join(this.tempDir, `${uuidv4()}.py`);

    const sandboxedCode = `
import sys
import json
import resource
import signal

# Set resource limits
resource.setrlimit(resource.RLIMIT_AS, (${this.memoryLimit}, ${this.memoryLimit}))
resource.setrlimit(resource.RLIMIT_CPU, (5, 5))

def timeout_handler(signum, frame):
    raise TimeoutError("Code execution timed out")

signal.signal(signal.SIGALRM, timeout_handler)
signal.alarm(5)

try:
    ${code}
    
    input_data = ${JSON.stringify(input)}
    result = None
    
    # Try common function names
    for func_name in ['solution', 'solve', 'main']:
        if func_name in globals() and callable(globals()[func_name]):
            func = globals()[func_name]
            if isinstance(input_data, dict):
                # Handle object inputs like {"s":"PAYPALISHIRING","numRows":3}
                result = func(**input_data)
            elif isinstance(input_data, list) and len(input_data) > 1:
                result = func(*input_data)
            else:
                result = func(input_data)
            break
    
    if result is None:
        result = input_data
    
    print(json.dumps(result, default=str))
    
except Exception as e:
    print(f"ERROR: {str(e)}", file=sys.stderr)
finally:
    signal.alarm(0)
`;

    try {
      fs.writeFileSync(tempFile, sandboxedCode);
      
      const result = await new Promise<{ output: any; error?: string; memoryUsage: number }>((resolve) => {
        const child = spawn('python3', [tempFile], {
          timeout: this.timeout,
          stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';
        let memoryUsage = 0;

        child.stdout.on('data', (data) => stdout += data.toString());
        child.stderr.on('data', (data) => stderr += data.toString());

        child.on('close', (code) => {
          if (stderr) {
            resolve({ output: null, error: stderr, memoryUsage });
          } else {
            try {
              const output = JSON.parse(stdout.trim());
              resolve({ output, memoryUsage });
            } catch {
              resolve({ output: stdout.trim(), memoryUsage });
            }
          }
        });

        child.on('error', (error) => {
          resolve({ output: null, error: error.message, memoryUsage });
        });
      });

      return {
        ...result,
        executionTime: Date.now() - startTime
      };
    } finally {
      try {
        if (fs.existsSync(tempFile)) {
          fs.unlinkSync(tempFile);
        }
      } catch (error) {
        console.warn('Failed to cleanup temp file immediately:', error);
      }
    }
  }

  async runJavaScript(code: string, input: any): Promise<{ output: any; error?: string; executionTime: number; memoryUsage: number }> {
    const startTime = Date.now();
    
    try {
      const vm = require('vm');
      const context = vm.createContext({
        input: input,
        result: undefined,
        console: { log: () => {}, error: () => {} }
      });

      const sandboxedCode = `
${code}

try {
  if (typeof solution === 'function') {
    if (typeof input === 'object' && !Array.isArray(input)) {
      result = solution(input.s, input.numRows || input.target || Object.values(input)[1]);
    } else {
      result = Array.isArray(input) && input.length > 1 ? solution(...input) : solution(input);
    }
  } else if (typeof solve === 'function') {
    if (typeof input === 'object' && !Array.isArray(input)) {
      result = solve(input.s, input.numRows || input.target || Object.values(input)[1]);
    } else {
      result = Array.isArray(input) && input.length > 1 ? solve(...input) : solve(input);
    }
  } else if (typeof main === 'function') {
    if (typeof input === 'object' && !Array.isArray(input)) {
      result = main(input.s, input.numRows || input.target || Object.values(input)[1]);
    } else {
      result = Array.isArray(input) && input.length > 1 ? main(...input) : main(input);
    }
  } else {
    result = input;
  }
} catch (e) {
  throw new Error(e.message);
}
`;

      vm.runInContext(sandboxedCode, context, {
        timeout: this.timeout,
        displayErrors: false
      });

      return {
        output: context.result,
        executionTime: Date.now() - startTime,
        memoryUsage: 0
      };
    } catch (error) {
      return {
        output: null,
        error: error.message,
        executionTime: Date.now() - startTime,
        memoryUsage: 0
      };
    }
  }
}