import fs from 'fs';
import path from 'path';

export class TempFileManager {
  private static instance: TempFileManager;
  private tempDirs: Set<string> = new Set();
  private cleanupInterval: NodeJS.Timeout;

  private constructor() {
    // Clean up temp files every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldFiles();
    }, 5 * 60 * 1000);

    // Clean up on process exit
    process.on('exit', () => this.cleanup());
    process.on('SIGINT', () => this.cleanup());
    process.on('SIGTERM', () => this.cleanup());
  }

  static getInstance(): TempFileManager {
    if (!TempFileManager.instance) {
      TempFileManager.instance = new TempFileManager();
    }
    return TempFileManager.instance;
  }

  registerTempDir(dirPath: string) {
    this.tempDirs.add(dirPath);
  }

  private cleanupOldFiles() {
    const maxAge = 10 * 60 * 1000; // 10 minutes
    const now = Date.now();

    this.tempDirs.forEach(dir => {
      if (!fs.existsSync(dir)) return;

      try {
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stats = fs.statSync(filePath);
          
          if (now - stats.mtime.getTime() > maxAge) {
            fs.unlinkSync(filePath);
            console.log(`Cleaned up old temp file: ${filePath}`);
          }
        });
      } catch (error) {
        console.error(`Error cleaning temp dir ${dir}:`, error);
      }
    });
  }

  cleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.tempDirs.forEach(dir => {
      if (!fs.existsSync(dir)) return;

      try {
        const files = fs.readdirSync(dir);
        files.forEach(file => {
          const filePath = path.join(dir, file);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        });
        fs.rmdirSync(dir);
      } catch (error) {
        console.error(`Error cleaning up temp dir ${dir}:`, error);
      }
    });
  }
}

// Initialize singleton
export const tempFileManager = TempFileManager.getInstance();