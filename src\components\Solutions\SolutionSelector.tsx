import React from 'react';

type SolutionType = 'brute_force' | 'less_optimal' | 'most_optimal';

interface SolutionSelectorProps {
  selectedSolution: SolutionType;
  onSolutionChange: (solution: SolutionType) => void;
  isSystemDesign?: boolean;
}

export const SolutionSelector: React.FC<SolutionSelectorProps> = ({
  selectedSolution,
  onSolutionChange,
  isSystemDesign = false
}) => {
  const solutions = isSystemDesign ? [
    { key: 'brute_force' as const, label: '🏗️ Basic (< 1K users)', color: 'bg-blue-500/20 border-blue-500/30', desc: 'Monolithic' },
    { key: 'less_optimal' as const, label: '🔧 Scalable (1K-100K)', color: 'bg-purple-500/20 border-purple-500/30', desc: 'Microservices' },
    { key: 'most_optimal' as const, label: '⚡ Enterprise (100K+)', color: 'bg-green-500/20 border-green-500/30', desc: 'Event-Driven' }
  ] : [
    { key: 'brute_force' as const, label: '🔴 Brute Force', color: 'bg-red-500/20 border-red-500/30', desc: 'O(n²)' },
    { key: 'less_optimal' as const, label: '🟡 Less Optimal', color: 'bg-yellow-500/20 border-yellow-500/30', desc: 'O(n log n)' },
    { key: 'most_optimal' as const, label: '🟢 Most Optimal', color: 'bg-green-500/20 border-green-500/30', desc: 'O(n)' }
  ];

  return (
    <div className="flex gap-2 mb-4 flex-wrap">
      {solutions.map(({ key, label, color, desc }) => (
        <button
          key={key}
          onClick={() => onSolutionChange(key)}
          className={`flex flex-col items-start gap-1 text-xs text-white rounded px-3 py-2 transition ${
            selectedSolution === key
              ? `${color} border`
              : 'bg-white/5 hover:bg-white/10 border border-transparent'
          }`}
        >
          <span className="font-medium">{label}</span>
          <span className="text-xs text-white/60">{desc}</span>
        </button>
      ))}
    </div>
  );
};