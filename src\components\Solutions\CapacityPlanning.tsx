import React from 'react';

interface CapacityPlanningProps {
  capacityPlanning?: {
    users: string;
    requestsPerSecond: string;
    estimatedCost: string;
  };
  techStack?: string[];
  scale?: string;
  architecture?: string;
}

export const CapacityPlanning: React.FC<CapacityPlanningProps> = ({
  capacityPlanning,
  techStack = [],
  scale,
  architecture
}) => {
  if (!capacityPlanning && !techStack.length && !scale && !architecture) {
    return null;
  }

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium text-white">📊 Capacity & Architecture</h3>
      
      <div className="grid grid-cols-2 gap-3">
        {scale && (
          <div className="bg-white/5 rounded-lg p-3">
            <div className="text-xs text-white/60 mb-1">Scale</div>
            <div className="text-sm text-white font-medium">{scale}</div>
          </div>
        )}
        
        {architecture && (
          <div className="bg-white/5 rounded-lg p-3">
            <div className="text-xs text-white/60 mb-1">Architecture</div>
            <div className="text-sm text-white font-medium">{architecture}</div>
          </div>
        )}
        
        {capacityPlanning?.users && (
          <div className="bg-white/5 rounded-lg p-3">
            <div className="text-xs text-white/60 mb-1">Users</div>
            <div className="text-sm text-white font-medium">{capacityPlanning.users}</div>
          </div>
        )}
        
        {capacityPlanning?.requestsPerSecond && (
          <div className="bg-white/5 rounded-lg p-3">
            <div className="text-xs text-white/60 mb-1">Requests/sec</div>
            <div className="text-sm text-white font-medium">{capacityPlanning.requestsPerSecond}</div>
          </div>
        )}
      </div>
      
      {capacityPlanning?.estimatedCost && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
          <div className="text-xs text-green-400 mb-1">💰 Estimated Cost</div>
          <div className="text-sm text-white font-medium">{capacityPlanning.estimatedCost}</div>
        </div>
      )}
      
      {techStack.length > 0 && (
        <div className="bg-white/5 rounded-lg p-3">
          <div className="text-xs text-white/60 mb-2">🛠️ Technology Stack</div>
          <div className="flex flex-wrap gap-1">
            {techStack.map((tech, index) => (
              <span
                key={index}
                className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};