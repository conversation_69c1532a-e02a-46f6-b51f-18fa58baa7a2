import { configHelper } from './ConfigHelper';
import { OpenAI } from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import axios from 'axios';

export interface TestCase {
  input: any;
  expectedOutput: any;
  description: string;
}

export class AITestGenerator {
  private openaiClient: OpenAI | null = null;
  private anthropicClient: Anthropic | null = null;
  private geminiApiKey: string | null = null;

  constructor() {
    this.initializeClients();
  }

  private initializeClients() {
    const config = configHelper.loadConfig();
    
    if (config.apiProvider === 'openai' && config.openaiApiKey) {
      this.openaiClient = new OpenAI({ apiKey: config.openaiApiKey });
    } else if (config.apiProvider === 'anthropic' && config.anthropicApiKey) {
      this.anthropicClient = new Anthropic({ apiKey: config.anthropicApiKey });
    } else if (config.apiProvider === 'gemini' && config.geminiApiKey) {
      this.geminiApiKey = config.geminiApiKey;
    }
  }

  async generateTestCases(problemStatement: string, language: string): Promise<TestCase[]> {
    const prompt = `Generate 3-5 comprehensive test cases for this coding problem:

PROBLEM: ${problemStatement}

Return ONLY a valid JSON array with this exact format:
[
  {
    "input": [2, 7, 11, 15, 9],
    "expectedOutput": [0, 1],
    "description": "Basic two sum case"
  }
]

IMPORTANT JSON RULES:
- Use only valid JSON values (numbers, strings, arrays, objects, true, false, null)
- NO Integer.MAX_VALUE, Integer.MIN_VALUE, or similar constants
- Use actual numbers like ********** instead of constants
- NO undefined values - use null instead
- Ensure all strings are properly quoted

Requirements:
- Include edge cases (empty input, single element, etc.)
- Include normal cases with expected behavior
- Include boundary cases (use actual numbers like 1000000)
- Make inputs realistic for ${language}
- Ensure outputs match the problem requirements exactly`;

    try {
      let response: string;

      if (this.openaiClient) {
        const result = await this.openaiClient.chat.completions.create({
          model: 'gpt-4o-mini',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 1000,
          temperature: 0.2
        });
        response = result.choices[0].message.content || '';
      } else if (this.anthropicClient) {
        const result = await this.anthropicClient.messages.create({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1000,
          messages: [{ role: 'user', content: prompt }]
        });
        response = (result.content[0] as any).text || '';
      } else if (this.geminiApiKey) {
        const result = await axios.post(
          `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${this.geminiApiKey}`,
          {
            contents: [{ role: 'user', parts: [{ text: prompt }] }],
            generationConfig: { temperature: 0.2, maxOutputTokens: 1000 }
          }
        );
        response = result.data.candidates[0].content.parts[0].text || '';
      } else {
        return this.generateFallbackTestCases(problemStatement, language);
      }

      // Parse AI response with better error handling
      try {
        // Clean the response first
        const cleanResponse = response.replace(/```json|```/g, '').trim();
        const jsonMatch = cleanResponse.match(/\[[\s\S]*\]/);
        
        if (jsonMatch) {
          // Additional cleaning for malformed JSON
          let jsonStr = jsonMatch[0]
            .replace(/Integer\.MAX_VALUE/g, '**********')
            .replace(/Integer\.MIN_VALUE/g, '-2147483648')
            .replace(/Long\.MAX_VALUE/g, '9223372036854775807')
            .replace(/Float\.MAX_VALUE/g, '3.4028235e+38')
            .replace(/Double\.MAX_VALUE/g, '1.7976931348623157e+308')
            .replace(/null/g, 'null')
            .replace(/undefined/g, 'null');
          
          const testCases = JSON.parse(jsonStr);
          if (Array.isArray(testCases)) {
            return testCases.map((tc: any) => ({
              input: tc.input || null,
              expectedOutput: tc.expectedOutput || null,
              description: tc.description || 'AI generated test case'
            }));
          }
        }
      } catch (parseError) {
        console.warn('Failed to parse AI test cases, using fallback:', parseError);
      }
    } catch (error) {
      console.error('AI test generation failed:', error);
    }

    return this.generateFallbackTestCases(problemStatement, language);
  }

  private generateFallbackTestCases(problemStatement: string, language: string): TestCase[] {
    const lower = problemStatement.toLowerCase();
    
    if (lower.includes('two sum')) {
      return [
        { input: [[2,7,11,15], 9], expectedOutput: [0,1], description: 'Basic two sum' },
        { input: [[3,2,4], 6], expectedOutput: [1,2], description: 'Two sum variant' },
        { input: [[3,3], 6], expectedOutput: [0,1], description: 'Duplicate numbers' }
      ];
    } else if (lower.includes('palindrome')) {
      return [
        { input: 'racecar', expectedOutput: true, description: 'Valid palindrome' },
        { input: 'hello', expectedOutput: false, description: 'Invalid palindrome' },
        { input: '', expectedOutput: true, description: 'Empty string' }
      ];
    } else if (lower.includes('reverse')) {
      return [
        { input: 'hello', expectedOutput: 'olleh', description: 'Basic reverse' },
        { input: 'a', expectedOutput: 'a', description: 'Single character' },
        { input: '', expectedOutput: '', description: 'Empty string' }
      ];
    } else {
      return [
        { input: [1,2,3], expectedOutput: [1,2,3], description: 'Array test' },
        { input: 'test', expectedOutput: 'test', description: 'String test' },
        { input: 42, expectedOutput: 42, description: 'Number test' }
      ];
    }
  }
}