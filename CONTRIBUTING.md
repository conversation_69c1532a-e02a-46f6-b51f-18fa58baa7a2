# Contributing to CodeCops 🚔

Thank you for your interest in contributing to CodeCops! This document outlines the rules and guidelines for developers working on this project.

## 📋 Development Rules

### 🔧 Code Standards

#### TypeScript/JavaScript
- **Use TypeScript** for all new code
- **Strict typing** - avoid `any` types unless absolutely necessary
- **ESLint compliance** - run `npm run lint` before committing
- **Consistent naming**: camelCase for variables/functions, PascalCase for components
- **No console.log** in production code - use proper logging

#### React Components
- **Functional components** with hooks only
- **Props interface** must be defined for all components
- **Error boundaries** for critical components
- **Proper cleanup** in useEffect hooks
- **Minimal re-renders** - optimize with useMemo/useCallback when needed

#### Electron Main Process
- **Proper IPC handling** - always validate data from renderer
- **Error handling** - wrap async operations in try-catch
- **Resource cleanup** - properly dispose of resources
- **Security first** - validate all external inputs

### 🎯 Feature Development

#### New Features
1. **Create issue first** - discuss before implementing
2. **Feature branch** - `feature/your-feature-name`
3. **Small commits** - atomic changes with clear messages
4. **Tests required** - add tests for new functionality
5. **Documentation** - update README if needed

#### AI Provider Integration
- **Follow existing pattern** in `ProcessingHelper.ts`
- **Add provider config** in `ConfigHelper.ts`
- **Update UI** in `SettingsDialog.tsx`
- **Error handling** for API failures
- **Rate limiting** consideration

### 🐛 Bug Fixes

#### Bug Fix Process
1. **Reproduce bug** - create minimal reproduction case
2. **Root cause analysis** - understand why it happened
3. **Fix branch** - `fix/bug-description`
4. **Test thoroughly** - ensure fix doesn't break other features
5. **Add regression test** if applicable

#### Critical Bugs
- **Security issues** - report privately first
- **Data loss bugs** - highest priority
- **Crash bugs** - immediate attention required

## 📝 Commit Guidelines

### Commit Message Format
```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

#### Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

#### Examples
```bash
feat(audio): add voice question processing with Gemini API
fix(screenshot): resolve Windows screenshot capture issues
docs(readme): update installation instructions
refactor(config): simplify API key management
```

### Commit Rules
- **One logical change** per commit
- **Clear description** - explain what and why
- **Reference issues** - use `Fixes #123` or `Closes #123`
- **No merge commits** - use rebase workflow

## 🔀 Pull Request Process

### Before Creating PR
1. **Update your branch** - rebase on latest main
2. **Run tests** - `npm test` and `npm run lint`
3. **Build successfully** - `npm run build`
4. **Test manually** - verify your changes work

### PR Requirements
- **Clear title** - describe the change
- **Detailed description** - what, why, how
- **Screenshots** for UI changes
- **Breaking changes** clearly marked
- **Linked issues** - reference related issues

### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Manual testing completed
- [ ] Automated tests pass
- [ ] Cross-platform testing (if applicable)

## Screenshots (if applicable)
Add screenshots for UI changes

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
```

## 🏗️ Architecture Guidelines

### Project Structure
```
├── electron/           # Main process code
│   ├── main.ts        # App initialization
│   ├── ProcessingHelper.ts  # AI integration
│   └── ConfigHelper.ts      # Configuration management
├── src/               # Renderer process
│   ├── components/    # React components
│   ├── pages/        # Main app views
│   └── types/        # TypeScript definitions
└── dist-electron/    # Built electron files
```

### Design Patterns
- **Single Responsibility** - one purpose per module
- **Dependency Injection** - pass dependencies explicitly
- **Error Boundaries** - graceful error handling
- **State Management** - use React Query for server state
- **Event-Driven** - use IPC for electron communication

## 🧪 Testing Requirements

### Test Coverage
- **New features** - must include tests
- **Bug fixes** - add regression tests
- **Critical paths** - screenshot, audio, AI processing
- **Error scenarios** - test failure cases

### Testing Tools
- **Unit tests** - Jest for business logic
- **Integration tests** - for AI provider integration
- **E2E tests** - for critical user flows
- **Manual testing** - cross-platform verification

## 🔒 Security Guidelines

### API Keys
- **Never commit** API keys or secrets
- **Use environment variables** for sensitive data
- **Validate inputs** from external sources
- **Sanitize outputs** before displaying

### Electron Security
- **Context isolation** enabled
- **Node integration** disabled in renderer
- **Content Security Policy** implemented
- **External URL validation** required

## 🚀 Release Process

### Version Numbering
- **Semantic versioning** - MAJOR.MINOR.PATCH
- **Breaking changes** - increment MAJOR
- **New features** - increment MINOR
- **Bug fixes** - increment PATCH

### Release Checklist
1. **Update version** in package.json
2. **Update CHANGELOG** with changes
3. **Create release tag** - `v1.0.0`
4. **GitHub Actions** will build automatically
5. **Test release** on all platforms

## 🚫 What NOT to Do

### Forbidden Practices
- ❌ **No direct commits** to main branch
- ❌ **No force push** to shared branches
- ❌ **No large files** in repository
- ❌ **No API keys** in code
- ❌ **No breaking changes** without discussion
- ❌ **No untested code** in production

### Code Quality
- ❌ **No duplicate code** - create reusable functions
- ❌ **No magic numbers** - use named constants
- ❌ **No deep nesting** - max 3 levels
- ❌ **No long functions** - keep under 50 lines
- ❌ **No unused imports** - clean up imports

## 🎯 Priority Areas

### High Priority
1. **Cross-platform compatibility** - Windows, macOS, Linux
2. **AI provider reliability** - error handling and fallbacks
3. **Performance optimization** - memory and CPU usage
4. **Security hardening** - input validation and sanitization

### Medium Priority
1. **UI/UX improvements** - better user experience
2. **Documentation** - comprehensive guides
3. **Testing coverage** - automated test suite
4. **Accessibility** - keyboard navigation, screen readers

### Low Priority
1. **Code refactoring** - improve maintainability
2. **Developer tools** - debugging and profiling
3. **Internationalization** - multi-language support
4. **Advanced features** - nice-to-have functionality

## 📞 Getting Help

### Communication Channels
- **GitHub Issues** - bug reports and feature requests
- **GitHub Discussions** - general questions and ideas
- **Pull Request Reviews** - code feedback and suggestions

### Code Review Process
1. **Self-review** - review your own code first
2. **Peer review** - at least one approval required
3. **Maintainer review** - final approval from maintainer
4. **Automated checks** - all CI checks must pass

## 📚 Resources

### Documentation
- [Electron Documentation](https://www.electronjs.org/docs)
- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [OpenAI API Reference](https://platform.openai.com/docs/api-reference)

### Tools
- **VS Code** - recommended editor with extensions
- **Git** - version control
- **Node.js 18+** - runtime environment
- **npm** - package manager

---

**Remember**: Quality over quantity. It's better to make one solid contribution than multiple rushed ones. Take time to understand the codebase and follow these guidelines for the best collaboration experience.

Happy coding! 🚀