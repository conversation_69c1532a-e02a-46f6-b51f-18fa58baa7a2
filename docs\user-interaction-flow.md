# User Interaction Flow Diagrams

## 🎮 User Journey State Machine

```mermaid
stateDiagram-v2
    [*] --> AppLaunch
    AppLaunch --> ConfigCheck
    
    state ConfigCheck <<choice>>
    ConfigCheck --> WelcomeScreen : No API Key
    ConfigCheck --> QueuePage : API Key Exists
    
    WelcomeScreen --> SettingsDialog
    SettingsDialog --> QueuePage : API Key Saved
    
    state QueuePage {
        [*] --> ModeSelection
        ModeSelection --> DSAMode
        ModeSelection --> SystemDesignMode
        ModeSelection --> AutoDetectMode
        
        DSAMode --> ScreenshotCapture
        SystemDesignMode --> ScreenshotCapture
        AutoDetectMode --> ScreenshotCapture
        
        ScreenshotCapture --> ScreenshotQueue
        ScreenshotQueue --> ProcessingTrigger
        ProcessingTrigger --> AIProcessing
    }
    
    AIProcessing --> SolutionsPage
    
    state SolutionsPage {
        [*] --> SolutionDisplay
        SolutionDisplay --> DSASolutionSelector : DSA Problem
        SolutionDisplay --> SystemDesignTabs : System Design Problem
        
        DSASolutionSelector --> BruteForceSolution
        DSASolutionSelector --> LessOptimalSolution
        DSASolutionSelector --> MostOptimalSolution
        
        SystemDesignTabs --> HLDTab
        SystemDesignTabs --> LLDTab
        SystemDesignTabs --> CodeTab
        SystemDesignTabs --> DiagramsTab
        
        BruteForceSolution --> DebugMode
        LessOptimalSolution --> DebugMode
        MostOptimalSolution --> DebugMode
        HLDTab --> DebugMode
        LLDTab --> DebugMode
        CodeTab --> DebugMode
        DiagramsTab --> DebugMode
    }
    
    DebugMode --> DebugScreenshots
    DebugScreenshots --> DebugProcessing
    DebugProcessing --> DebugResults
    DebugResults --> SolutionsPage
    
    SolutionsPage --> QueuePage : Reset (Ctrl+R)
    QueuePage --> [*] : Quit (Ctrl+Q)
```

## 🎯 Keyboard Shortcuts Flow

```mermaid
flowchart TD
    GlobalShortcuts[Global Shortcuts Registered] --> KeyPress{Key Pressed?}
    
    KeyPress -->|Ctrl+H| TakeScreenshot[Take Screenshot]
    KeyPress -->|Ctrl+Enter| ProcessScreenshots[Process Screenshots]
    KeyPress -->|Ctrl+M| ToggleMode[Toggle Question Mode]
    KeyPress -->|Ctrl+B| ToggleVisibility[Toggle Window Visibility]
    KeyPress -->|Ctrl+R| ResetApp[Reset Application]
    KeyPress -->|Ctrl+L| DeleteLast[Delete Last Screenshot]
    KeyPress -->|Ctrl+Q| QuitApp[Quit Application]
    KeyPress -->|Ctrl+Arrows| MoveWindow[Move Window]
    KeyPress -->|Ctrl+Brackets| AdjustOpacity[Adjust Opacity]
    
    TakeScreenshot --> UpdateUI[Update UI with New Screenshot]
    ProcessScreenshots --> StartAIProcessing[Start AI Processing]
    ToggleMode --> CycleModes[Cycle: DSA to System Design to Auto]
    ToggleVisibility --> ShowHideWindow[Show/Hide Window]
    ResetApp --> ClearAllData[Clear All Data & Reset]
    DeleteLast --> RemoveScreenshot[Remove Last Screenshot]
    QuitApp --> CloseApplication[Close Application]
    MoveWindow --> UpdatePosition[Update Window Position]
    AdjustOpacity --> UpdateOpacity[Update Window Opacity]
    
    UpdateUI --> WaitForInput[Wait for Next Input]
    StartAIProcessing --> WaitForInput
    CycleModes --> WaitForInput
    ShowHideWindow --> WaitForInput
    ClearAllData --> WaitForInput
    RemoveScreenshot --> WaitForInput
    UpdatePosition --> WaitForInput
    UpdateOpacity --> WaitForInput
    
    WaitForInput --> KeyPress
```

## 🔄 Screenshot Processing Workflow

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant Main
    participant Screenshot
    participant AI
    participant Storage
    
    User->>UI: Press Ctrl+H
    UI->>Main: Trigger Screenshot
    Main->>Screenshot: Hide Window
    Screenshot->>Screenshot: Capture Screen
    Screenshot->>Storage: Save Screenshot File
    Screenshot->>Main: Show Window
    Main->>UI: Send Screenshot Data
    UI->>UI: Display Screenshot Preview
    
    User->>UI: Press Ctrl+Enter
    UI->>Main: Process Screenshots
    Main->>AI: Send Images + Prompt
    AI->>AI: Analyze & Generate
    AI->>Main: Return Solutions
    Main->>UI: Send Solution Data
    UI->>UI: Navigate to Solutions Page
    UI->>User: Display Solutions
```

## 🎨 UI Component Interaction

```mermaid
graph TD
    subgraph "Queue Page Components"
        QueuePage[Queue.tsx]
        ModeToggle[Mode Toggle Buttons]
        ScreenshotQueue[Screenshot Queue]
        QueueCommands[Queue Commands]
        VoiceRecorder[Voice Recorder]
    end
    
    subgraph "Solutions Page Components"
        SolutionsPage[Solutions.tsx]
        SolutionSelector[Solution Selector]
        CodeDisplay[Code Display]
        ThoughtsDisplay[Thoughts Display]
        ComplexityDisplay[Complexity Display]
        SystemDesignTabs[System Design Tabs]
    end
    
    subgraph "Shared Components"
        SettingsDialog[Settings Dialog]
        NotesDialog[Notes Dialog]
        ToastNotifications[Toast Notifications]
        LoadingSpinner[Loading Spinner]
    end
    
    QueuePage --> ModeToggle
    QueuePage --> ScreenshotQueue
    QueuePage --> QueueCommands
    QueuePage --> VoiceRecorder
    
    SolutionsPage --> SolutionSelector
    SolutionsPage --> CodeDisplay
    SolutionsPage --> ThoughtsDisplay
    SolutionsPage --> ComplexityDisplay
    SolutionsPage --> SystemDesignTabs
    
    QueuePage --> SettingsDialog
    QueuePage --> NotesDialog
    QueuePage --> ToastNotifications
    QueuePage --> LoadingSpinner
    
    SolutionsPage --> SettingsDialog
    SolutionsPage --> NotesDialog
    SolutionsPage --> ToastNotifications
    SolutionsPage --> LoadingSpinner
    
    ModeToggle --> |Mode Change| QueueCommands
    ScreenshotQueue --> |Screenshot Action| QueueCommands
    SolutionSelector --> |Solution Change| CodeDisplay
    SolutionSelector --> |Solution Change| ThoughtsDisplay
    SolutionSelector --> |Solution Change| ComplexityDisplay
```