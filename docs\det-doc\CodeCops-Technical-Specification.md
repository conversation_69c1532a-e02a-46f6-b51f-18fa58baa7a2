# CodeCops Technical Specification

## 1. Title
**CodeCops - AI-Powered Technical Interview Assistant**
*Version 1.0.19 - Complete System Specification*

---

## 2. Overview / Purpose

CodeCops is an invisible, open-source desktop application designed to assist users during technical interviews using AI-powered analysis. The application provides real-time assistance through screenshot analysis, voice question processing, and multi-provider AI integration while remaining undetectable by common screen sharing platforms.

### Core Mission
- **99% Invisible**: Undetectable by Zoom, Discord, and browser screen sharing
- **AI-Powered**: Multi-provider support (OpenAI, Gemini, Claude, Grok)
- **Real-time**: Instant screenshot analysis and voice question processing
- **Comprehensive**: Supports DSA problems, system design, and debugging

---

## 3. Scope

### In Scope
- Desktop application for Windows, macOS, and Linux
- Screenshot capture and AI analysis
- Voice recording and transcription
- Multi-AI provider integration (OpenAI, Gemini, Anthropic, Grok)
- Question type detection (DSA vs System Design)
- Three-tier solution generation (Brute Force, Less Optimal, Most Optimal)
- Code validation and test case generation
- Global keyboard shortcuts
- Invisible window management
- Configuration management

### Out of Scope
- Web-based version
- Mobile applications
- Cloud-based processing (all processing is local + AI APIs)
- User authentication/accounts
- Data synchronization across devices

---

## 4. Requirements

### 4.1 Functional Requirements

#### Core Features
- **FR-001**: Screenshot capture with configurable global shortcuts
- **FR-002**: AI-powered problem extraction from screenshots
- **FR-003**: Multi-complexity solution generation (O(n²), O(n log n), O(n))
- **FR-004**: Voice question recording and transcription
- **FR-005**: Question type auto-detection (DSA/System Design)
- **FR-006**: Code validation with test case generation
- **FR-007**: Debug assistance through additional screenshots
- **FR-008**: Multi-language programming support

#### AI Integration
- **FR-009**: OpenAI GPT-4o/4o-mini integration
- **FR-010**: Google Gemini 1.5 Pro/2.0 Flash integration
- **FR-011**: Anthropic Claude 3.5/3.7 Sonnet integration
- **FR-012**: Grok X1 integration
- **FR-013**: Provider-specific model selection
- **FR-014**: API key validation and testing

#### User Interface
- **FR-015**: Invisible window mode with opacity control
- **FR-016**: Global keyboard shortcuts for all functions
- **FR-017**: Settings dialog for configuration
- **FR-018**: Notes dialog for temporary storage
- **FR-019**: Solution selector with three complexity levels
- **FR-020**: Progress indicators during processing

### 4.2 Non-Functional Requirements

#### Performance
- **NFR-001**: Screenshot capture < 500ms
- **NFR-002**: AI processing response < 30 seconds
- **NFR-003**: Memory usage < 200MB during idle
- **NFR-004**: CPU usage < 5% during idle

#### Security
- **NFR-005**: API keys stored locally and encrypted
- **NFR-006**: No telemetry or usage tracking
- **NFR-007**: Screenshots processed locally, sent only to chosen AI provider
- **NFR-008**: Secure handling of temporary files

#### Compatibility
- **NFR-009**: Windows 10/11 support
- **NFR-010**: macOS 10.15+ support
- **NFR-011**: Linux Ubuntu 18.04+ support
- **NFR-012**: Invisible to Zoom < 6.1.6, Discord, browser screen sharing

#### Reliability
- **NFR-013**: 99.9% uptime during usage sessions
- **NFR-014**: Graceful error handling and recovery
- **NFR-015**: Automatic memory cleanup every 10 minutes

---

## 5. Architecture / Design

### 5.1 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    CodeCops Application                     │
├─────────────────────┬───────────────────────────────────────┤
│   Electron Main     │         Renderer Process              │
│   Process           │         (React + TypeScript)         │
├─────────────────────┼───────────────────────────────────────┤
│ • Window Management │ • User Interface                      │
│ • Screenshot Capture│ • Solution Display                    │
│ • AI Processing     │ • Settings Management                 │
│ • Global Shortcuts  │ • Queue Management                    │
│ • IPC Handlers      │ • Audio Recording                     │
└─────────────────────┴───────────────────────────────────────┘
                              │
                    ┌─────────┴─────────┐
                    │   External APIs   │
                    ├───────────────────┤
                    │ • OpenAI GPT-4o   │
                    │ • Gemini 2.0      │
                    │ • Claude 3.7      │
                    │ • Grok X1         │
                    └───────────────────┘
```

### 5.2 Component Architecture

#### Main Process Components
```typescript
interface MainProcessArchitecture {
  main: {
    windowManagement: WindowManager;
    applicationState: AppState;
    helpers: {
      processingHelper: ProcessingHelper;
      screenshotHelper: ScreenshotHelper;
      shortcutsHelper: ShortcutsHelper;
      configHelper: ConfigHelper;
    };
  };
}
```

#### Renderer Process Components
```typescript
interface RendererArchitecture {
  pages: {
    queue: QueuePage;
    solutions: SolutionsPage;
    debug: DebugPage;
  };
  components: {
    ui: UIComponents;
    solutions: SolutionComponents;
    settings: SettingsComponents;
    audio: AudioComponents;
  };
}
```

### 5.3 Data Flow Diagram

```
User Action → Global Shortcut → Main Process → Helper Classes
     ↓
Screenshot/Audio Capture → AI Processing → Response Parsing
     ↓
IPC Communication → Renderer Update → UI Display
```

---

## 6. API Design

### 6.1 IPC (Inter-Process Communication) API

#### Configuration Management
```typescript
interface ConfigAPI {
  'get-config': () => Promise<Config>;
  'update-config': (updates: Partial<Config>) => Promise<Config>;
  'check-api-key': () => Promise<boolean>;
  'validate-api-key': (apiKey: string) => Promise<{valid: boolean, error?: string}>;
}
```

#### Screenshot Management
```typescript
interface ScreenshotAPI {
  'take-screenshot': () => Promise<{path: string, preview: string}>;
  'get-screenshots': () => Promise<Array<{path: string, preview: string}>>;
  'delete-screenshot': (path: string) => Promise<{success: boolean, error?: string}>;
  'process-screenshots': () => Promise<void>;
}
```

#### Audio Processing
```typescript
interface AudioAPI {
  'process-audio': (data: string, mimeType: string) => Promise<{
    transcription: string;
    isQuestion: boolean;
    questionType?: string;
    response?: string;
    timestamp: number;
  }>;
}
```

#### Code Validation
```typescript
interface ValidationAPI {
  'validate-code': (params: {
    code: string;
    language: string;
    testCases: TestCase[];
    problemStatement: string;
  }) => Promise<ValidationResult>;
  'generate-test-cases': (params: {
    problemStatement: string;
    language: string;
  }) => Promise<TestCase[]>;
}
```

### 6.2 AI Provider APIs

#### OpenAI Integration
```typescript
interface OpenAIAPI {
  chat: {
    completions: {
      create: (params: ChatCompletionParams) => Promise<ChatCompletion>;
    };
  };
  audio: {
    transcriptions: {
      create: (params: TranscriptionParams) => Promise<Transcription>;
    };
  };
}
```

#### Gemini Integration
```typescript
interface GeminiAPI {
  generateContent: (params: {
    contents: GeminiMessage[];
    generationConfig: GenerationConfig;
  }) => Promise<GeminiResponse>;
}
```

---

## 7. Data Models

### 7.1 Core Data Models

#### Configuration Model
```typescript
interface Config {
  apiKey: string; // Legacy field
  apiProvider: "openai" | "gemini" | "anthropic" | "grok";
  openaiApiKey: string;
  geminiApiKey: string;
  anthropicApiKey: string;
  grokApiKey: string;
  extractionModel: string;
  solutionModel: string;
  debuggingModel: string;
  language: string;
  opacity: number;
  questionDetectionMode: "dsa" | "system-design" | "auto-detect";
  systemDesignFocus: "hld" | "lld" | "both";
}
```

#### Problem Information Model
```typescript
interface ProblemInfo {
  problem_statement: string;
  constraints?: string;
  example_input?: string;
  example_output?: string;
  question_type: "coding" | "system-design";
  difficulty_level?: "easy" | "medium" | "hard";
  suggested_approaches?: string[];
  functional_requirements?: string[];
  non_functional_requirements?: Record<string, string>;
  scale_requirements?: Record<string, string>;
  system_components?: string[];
  detection_confidence?: number;
}
```

#### Solution Model
```typescript
interface Solution {
  code: string;
  thoughts: string[];
  time_complexity: string;
  space_complexity: string;
}

interface SolutionsResponse {
  solutions: {
    brute_force: Solution;
    less_optimal: Solution;
    most_optimal: Solution;
  };
  selectedSolution: 'brute_force' | 'less_optimal' | 'most_optimal';
  isSystemDesign?: boolean;
  fullResponse?: string;
}
```

#### Test Case Model
```typescript
interface TestCase {
  input: any;
  expectedOutput: any;
  description: string;
}
```

### 7.2 Application State Model

```typescript
interface AppState {
  mainWindow: BrowserWindow | null;
  isWindowVisible: boolean;
  windowPosition: { x: number; y: number } | null;
  windowSize: { width: number; height: number } | null;
  view: "queue" | "solutions" | "debug";
  problemInfo: ProblemInfo | null;
  hasDebugged: boolean;
  screenshotHelper: ScreenshotHelper | null;
  processingHelper: ProcessingHelper | null;
  shortcutsHelper: ShortcutsHelper | null;
}
```

---

## 8. UI/UX Details

### 8.1 Window Management

#### Invisible Mode Features
- **Opacity Control**: 0.1 to 1.0 with keyboard shortcuts
- **Always on Top**: Screen-saver level priority
- **Content Protection**: Prevents screenshot capture
- **Skip Taskbar**: Hidden from taskbar and Alt+Tab

#### Window Positioning
- **Keyboard Movement**: Arrow keys with Ctrl/Cmd
- **Boundary Constraints**: 2/3 off-screen allowance
- **Multi-monitor Support**: Primary display detection

### 8.2 User Interface Components

#### Queue View
```typescript
interface QueueView {
  components: {
    header: HeaderComponent;
    screenshotQueue: ScreenshotQueueComponent;
    queueCommands: QueueCommandsComponent;
    voiceRecorder: VoiceRecorderComponent;
    languageSelector: LanguageSelectorComponent;
  };
}
```

#### Solutions View
```typescript
interface SolutionsView {
  components: {
    solutionSelector: SolutionSelectorComponent;
    codeDisplay: CodeDisplayComponent;
    performanceMetrics: PerformanceMetricsComponent;
    validationStatus: ValidationStatusComponent;
    testCaseEditor: TestCaseEditorComponent;
  };
}
```

### 8.3 Global Shortcuts

| Action | Windows/Linux | macOS | Function |
|--------|---------------|-------|----------|
| Toggle Visibility | `Ctrl + B` | `Cmd + B` | Show/hide window |
| Take Screenshot | `Ctrl + H` | `Cmd + H` | Capture screen |
| Process Screenshots | `Ctrl + Enter` | `Cmd + Enter` | Analyze with AI |
| Toggle Question Mode | `Ctrl + M` | `Cmd + M` | Switch DSA/System Design |
| Reset/New Problem | `Ctrl + R` | `Cmd + R` | Clear all data |
| Delete Last Screenshot | `Ctrl + L` | `Cmd + L` | Remove recent capture |
| Brute Force Solution | `Ctrl + Shift + B` | `Cmd + Shift + B` | Show O(n²) solution |
| Less Optimal Solution | `Ctrl + Shift + L` | `Cmd + Shift + L` | Show O(n log n) solution |
| Most Optimal Solution | `Ctrl + Shift + M` | `Cmd + Shift + M` | Show O(n) solution |
| Settings | `Ctrl + Shift + S` | `Cmd + Shift + S` | Open settings dialog |
| Notes | `Ctrl + N` | `Cmd + N` | Open notes dialog |
| Code Validation | `Ctrl + T` | `Cmd + T` | Validate current code |

---

## 9. Security Considerations

### 9.1 Data Protection

#### API Key Security
- **Local Storage**: Keys stored in encrypted local config files
- **No Transmission**: Keys never sent except to chosen AI provider
- **Format Validation**: Strict regex validation for each provider
- **Auto-detection**: Provider detection based on key format

#### Screenshot Security
- **Temporary Storage**: Screenshots deleted after processing
- **Local Processing**: No cloud storage of sensitive data
- **Memory Cleanup**: Automatic garbage collection every 10 minutes

### 9.2 Privacy Measures

#### Zero Telemetry
- **No Tracking**: No usage analytics or data collection
- **No Accounts**: No user authentication or cloud accounts
- **Local Only**: All data processing happens locally

#### Screen Sharing Protection
- **Content Protection**: Electron content protection enabled
- **Window Flags**: Hidden from mission control and screen recording
- **Opacity Management**: Minimum 30% opacity for recovery

---

## 10. Error Handling

### 10.1 API Error Handling

#### OpenAI Errors
```typescript
interface OpenAIErrorHandling {
  401: "Invalid API key";
  429: "Rate limit exceeded or insufficient credits";
  500: "Server error - retry with backoff";
  timeout: "Request timeout after 30 seconds";
}
```

#### Gemini Errors
```typescript
interface GeminiErrorHandling {
  429: "Rate limit exceeded - wait 60 seconds";
  400: "Invalid request format";
  403: "API key invalid or quota exceeded";
}
```

### 10.2 Application Error Handling

#### Screenshot Errors
- **Capture Failure**: Multiple fallback methods on Windows
- **File System**: Graceful handling of permission issues
- **Memory**: Automatic cleanup on low memory conditions

#### Processing Errors
- **Network Issues**: Retry with exponential backoff
- **Parsing Failures**: Fallback to basic code extraction
- **Timeout Handling**: 30-second timeout with user notification

### 10.3 Recovery Mechanisms

#### Emergency Recovery
- **Ctrl+Shift+R**: Force reset all application state
- **Opacity Recovery**: Minimum 30% opacity enforcement
- **Window Recovery**: Force show and focus window

---

## 11. Performance Requirements

### 11.1 Response Time Requirements

| Operation | Target Time | Maximum Time |
|-----------|-------------|--------------|
| Screenshot Capture | < 500ms | < 1000ms |
| AI Problem Extraction | < 15s | < 30s |
| Solution Generation | < 20s | < 45s |
| Code Validation | < 5s | < 10s |
| Voice Transcription | < 10s | < 20s |

### 11.2 Resource Usage Limits

| Resource | Idle Usage | Active Usage | Maximum |
|----------|------------|--------------|---------|
| Memory | < 100MB | < 200MB | < 500MB |
| CPU | < 2% | < 15% | < 30% |
| Disk Space | < 50MB | < 100MB | < 200MB |
| Network | 0 KB/s | Variable | API limits |

### 11.3 Scalability Considerations

#### Queue Management
- **Maximum Screenshots**: 5 per queue (main + extra)
- **Automatic Cleanup**: Old screenshots removed automatically
- **Memory Management**: Garbage collection every 10 minutes

#### API Rate Limiting
- **Request Queue**: Sequential API requests to prevent rate limiting
- **Retry Logic**: Exponential backoff for failed requests
- **Provider Switching**: Fallback between AI providers

---

## 12. Dependencies

### 12.1 Core Dependencies

#### Electron Framework
```json
{
  "electron": "^29.1.4",
  "electron-builder": "^24.13.3",
  "electron-updater": "^6.3.9"
}
```

#### React Frontend
```json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "@vitejs/plugin-react": "^4.2.1",
  "vite": "^6.2.5"
}
```

#### AI Providers
```json
{
  "openai": "^4.28.4",
  "@anthropic-ai/sdk": "^0.39.0",
  "axios": "^1.7.7"
}
```

#### UI Components
```json
{
  "@radix-ui/react-dialog": "^1.1.2",
  "@radix-ui/react-toast": "^1.2.2",
  "tailwindcss": "^3.4.15",
  "lucide-react": "^0.460.0"
}
```

### 12.2 System Dependencies

#### Windows
- **Node.js**: 16+
- **Python**: 3.x for native modules
- **Visual Studio Build Tools**: For native compilation
- **PowerShell**: For screenshot capture fallback

#### macOS
- **Node.js**: 16+
- **Xcode Command Line Tools**: For native compilation
- **Screen Recording Permission**: For screenshot capture

#### Linux
- **Node.js**: 16+
- **Build Essential**: For native compilation
- **X11**: For screenshot capture
- **GNOME/KDE**: Desktop environment support

---

## 13. Testing Strategy

### 13.1 Unit Testing

#### Core Components
```typescript
interface UnitTestCoverage {
  configHelper: {
    apiKeyValidation: TestSuite;
    configPersistence: TestSuite;
    providerDetection: TestSuite;
  };
  processingHelper: {
    aiIntegration: TestSuite;
    responseParser: TestSuite;
    errorHandling: TestSuite;
  };
  screenshotHelper: {
    captureLogic: TestSuite;
    queueManagement: TestSuite;
    fileOperations: TestSuite;
  };
}
```

### 13.2 Integration Testing

#### AI Provider Integration
- **OpenAI API**: Test all models and endpoints
- **Gemini API**: Test content generation and audio processing
- **Anthropic API**: Test Claude models and rate limiting
- **Grok API**: Test vision and text generation

#### Platform Testing
- **Windows**: Test on Windows 10/11 with different configurations
- **macOS**: Test on Intel and Apple Silicon Macs
- **Linux**: Test on Ubuntu, Fedora, and Arch Linux

### 13.3 End-to-End Testing

#### User Workflows
1. **First Launch**: API key setup and configuration
2. **Screenshot Analysis**: Capture → Process → Display solutions
3. **Voice Questions**: Record → Transcribe → Generate response
4. **Code Validation**: Input code → Generate tests → Validate
5. **Debug Workflow**: Additional screenshots → Debug analysis

#### Edge Cases
- **Network Failures**: Offline mode and reconnection
- **API Limits**: Rate limiting and quota exhaustion
- **Large Screenshots**: Memory management with large images
- **Multiple Monitors**: Screenshot capture across displays

---

## 14. Rollout Plan

### 14.1 Release Strategy

#### Phase 1: Core Features (v1.0.0)
- ✅ Basic screenshot capture and AI analysis
- ✅ OpenAI integration
- ✅ Windows/macOS support

#### Phase 2: Enhanced AI (v1.0.10)
- ✅ Multi-provider support (Gemini, Claude, Grok)
- ✅ Voice question processing
- ✅ Code validation

#### Phase 3: Advanced Features (v1.0.19)
- ✅ Question type detection
- ✅ Three-tier solution generation
- ✅ System design support
- ✅ Enhanced debugging

#### Phase 4: Future Enhancements (v1.1.0+)
- 🔄 Plugin system for custom AI providers
- 🔄 Advanced code execution environment
- 🔄 Interview simulation mode
- 🔄 Performance analytics

### 14.2 Distribution Channels

#### GitHub Releases
- **Primary Distribution**: GitHub releases with automated builds
- **Artifact Types**: .exe (Windows), .dmg (macOS), .AppImage (Linux)
- **Auto-updater**: Electron-updater for seamless updates

#### Package Managers
- **Windows**: Chocolatey package (future)
- **macOS**: Homebrew cask (future)
- **Linux**: Snap/Flatpak packages (future)

---

## 15. Open Questions / Risks

### 15.1 Technical Risks

#### AI Provider Reliability
- **Risk**: API downtime or rate limiting
- **Mitigation**: Multi-provider fallback system
- **Status**: Partially mitigated with 4 providers

#### Screen Sharing Detection
- **Risk**: Future updates to Zoom/Discord may detect the app
- **Mitigation**: Continuous testing and stealth improvements
- **Status**: Ongoing monitoring required

#### Performance on Low-end Hardware
- **Risk**: High memory/CPU usage on older systems
- **Mitigation**: Optimization and resource monitoring
- **Status**: Needs more testing on various hardware

### 15.2 Business Risks

#### API Cost Management
- **Risk**: High API costs for users
- **Mitigation**: Efficient prompt engineering and caching
- **Status**: User responsibility for API costs

#### Legal Considerations
- **Risk**: Use in actual interviews may violate terms
- **Mitigation**: Clear documentation about intended use
- **Status**: User responsibility for ethical use

### 15.3 Open Technical Questions

1. **Code Execution Security**: How to safely execute user code for validation?
2. **Advanced OCR**: Should we implement local OCR for better text extraction?
3. **Plugin Architecture**: How to allow third-party AI provider plugins?
4. **Mobile Support**: Is there demand for mobile companion apps?
5. **Cloud Sync**: Should we add optional cloud synchronization for settings?

---

## Appendices

### Appendix A: Global Shortcuts Reference

[Complete keyboard shortcuts table as shown in section 8.3]

### Appendix B: AI Provider Comparison

| Feature | OpenAI | Gemini | Claude | Grok |
|---------|--------|--------|--------|------|
| Vision API | ✅ GPT-4o | ✅ 2.0 Flash | ✅ 3.7 Sonnet | ✅ Vision |
| Audio Processing | ✅ Whisper | ✅ Native | ❌ | ❌ |
| Context Length | 128K tokens | 2M tokens | 200K tokens | 128K tokens |
| Rate Limits | Moderate | Generous | Conservative | Moderate |
| Cost | $$$ | $$ | $$$ | $$ |

### Appendix C: Build Instructions

[Detailed build instructions for each platform as shown in README.md]

### Appendix D: Configuration Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "apiProvider": {
      "type": "string",
      "enum": ["openai", "gemini", "anthropic", "grok"]
    },
    "extractionModel": {"type": "string"},
    "solutionModel": {"type": "string"},
    "debuggingModel": {"type": "string"},
    "language": {"type": "string"},
    "opacity": {"type": "number", "minimum": 0.1, "maximum": 1.0},
    "questionDetectionMode": {
      "type": "string",
      "enum": ["dsa", "system-design", "auto-detect"]
    }
  }
}
```

---

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Authors**: CodeCops Development Team  
**Review Status**: Complete  
**Next Review**: Q2 2025