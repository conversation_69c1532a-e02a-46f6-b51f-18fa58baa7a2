import React from 'react';
import { Button } from './ui/button';

interface WelcomeScreenProps {
  onOpenSettings: () => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onOpenSettings }) => {
  return (
    <div className="bg-black min-h-screen flex flex-col items-center justify-center p-6">
      <div className="max-w-md w-full bg-black border border-white/10 rounded-xl p-6 shadow-lg">
        <h1 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
          <span>CodeCops</span>
          <span className="text-sm font-normal px-2 py-0.5 bg-blue-500/20 text-blue-400 rounded-md">AI Assistant</span>
        </h1>
        
        <div className="mb-8">
          <h2 className="text-lg font-medium text-white mb-3">Welcome to CodeCops</h2>
          <p className="text-white/70 text-sm mb-4">
            Your AI-powered technical interview assistant. Get instant help with coding problems,
            system design questions, and technical explanations.
          </p>
          <div className="bg-white/5 border border-white/10 rounded-lg p-4 mb-4">
            <h3 className="text-white/90 font-medium mb-2">Global Shortcuts</h3>
            <ul className="space-y-2">
              <li className="flex justify-between text-sm">
                <span className="text-white/70">Toggle Visibility</span>
                <span className="text-white/90">Ctrl+B / Cmd+B</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="text-white/70">Take Screenshot</span>
                <span className="text-white/90">Ctrl+H / Cmd+H</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="text-white/70">Delete Last Screenshot</span>
                <span className="text-white/90">Ctrl+L / Cmd+L</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="text-white/70">Process Screenshots</span>
                <span className="text-white/90">Ctrl+Enter / Cmd+Enter</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="text-white/70">Reset View</span>
                <span className="text-white/90">Ctrl+R / Cmd+R</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="text-white/70">Quit App</span>
                <span className="text-white/90">Ctrl+Q / Cmd+Q</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="text-white/70">Brute Force Solution</span>
                <span className="text-white/90">Ctrl+Shift+B / Cmd+Shift+B</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="text-white/70">Less Optimal Solution</span>
                <span className="text-white/90">Ctrl+Shift+L / Cmd+Shift+L</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="text-white/70">Most Optimal Solution</span>
                <span className="text-white/90">Ctrl+Shift+M / Cmd+Shift+M</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="bg-white/5 border border-white/10 rounded-lg p-4 mb-6">
          <h3 className="text-white/90 font-medium mb-2">Getting Started</h3>
          <p className="text-white/70 text-sm mb-3">
            Configure your AI provider (OpenAI, Gemini, or Claude) to get started.
          </p>
          <Button 
            className="w-full px-4 py-3 bg-white text-black rounded-xl font-medium hover:bg-white/90 transition-colors flex items-center justify-center gap-2"
            onClick={onOpenSettings}
          >
            Open Settings
          </Button>
        </div>
        
        <div className="text-white/40 text-xs text-center">
          Take screenshots or record voice questions to get AI assistance
        </div>
      </div>
    </div>
  );
};