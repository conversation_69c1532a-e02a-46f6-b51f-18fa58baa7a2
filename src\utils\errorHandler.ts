import { APIError, ProcessingError, APIErrorHandler } from '../types/errors';

// At the top of src/utils/errorHandler.ts
export const RETRYABLE_STATUS_CODES = [429, 500, 502, 503];

export const createAPIError = (error: any, provider: string): APIError => ({
  name: 'APIError',
  message: error.message || 'API request failed',
  status: error.response?.status || error.status,
  provider: provider as any,
  retryable: RETRYABLE_STATUS_CODES.includes(error.response?.status || error.status),
  stack: error.stack
});

export const createProcessingError = (message: string, type: ProcessingError['type']): ProcessingError => ({
  name: 'ProcessingError',
  message,
  type,
  recoverable: type !== 'config'
});

export const withErrorHandling = async <T>(
  fn: () => Promise<T>,
  provider?: string
): Promise<{ success: true; data: T } | { success: false; error: string; retryable?: boolean }> => {
  try {
    const data = await fn();
    return { success: true, data };
  } catch (error: any) {
    if (provider) {
      const apiError = createAPIError(error, provider);
      const handled = APIErrorHandler.handle(apiError, provider);
      return { 
        success: false, 
        error: handled.message,
        retryable: handled.shouldRetry
      };
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};