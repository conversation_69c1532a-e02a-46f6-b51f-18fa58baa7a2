---
name: Bug Report
about: Create a report to help us improve CodeCops
title: '[BUG] '
labels: bug
assignees: ''
---

# 🐛 Bug Report

## 📋 Bug Description
<!-- A clear and concise description of what the bug is -->

## 🔄 Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. See error

## ✅ Expected Behavior
<!-- What you expected to happen -->

## ❌ Actual Behavior
<!-- What actually happened -->

## 🖥️ Environment
- **OS**: [Windows 11/macOS 13/Ubuntu 22.04]
- **CodeCops Version**: [v1.0.19]
- **AI Provider**: [OpenAI/Gemini/Anthropic]
- **Model**: [gpt-4o/gemini-1.5-pro/claude-3-sonnet]

## 📊 Error Details
```
Paste console errors here
```

## 📸 Screenshots
<!-- Add screenshots if applicable -->

## 🔍 Additional Context
- [ ] Happens every time
- [ ] Can reproduce consistently
- [ ] Checked troubleshooting guide
- [ ] Searched existing issues